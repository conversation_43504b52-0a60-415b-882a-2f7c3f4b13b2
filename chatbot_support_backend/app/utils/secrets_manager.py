import os
import boto3
import json
import logging
import time
from typing import Dict, Any

# Configure logging
logger = logging.getLogger(__name__)

def get_db_credentials() -> Dict[str, Any]:
    """
    Get database credentials based on environment.

    Returns:
        Dictionary containing database connection parameters
    """
    env = os.getenv('ENV', 'local')
    logger.info(f"Getting database credentials for environment: {env}")

    if env == 'local':
        creds = {
            "host": os.getenv("DB_HOST", "db"),
            "port": int(os.getenv("DB_PORT", "3306")),
            "user": os.getenv("DB_USER", "raguser"),
            "password": os.getenv("DB_PASSWORD", "ragpassword"),
            "database": os.getenv("DB_NAME", "ragchatbot")
        }
        logger.info(f"Using local database credentials for host: {creds['host']}")
        return creds
    else:
        # Production environment - use AWS Secrets Manager
        secret_arn = os.getenv("DB_SECRET_ARN")
        if not secret_arn:
            raise ValueError("DB_SECRET_ARN environment variable is required for production environment")

        logger.info(f"Retrieving database credentials from AWS Secrets Manager: {secret_arn}")

        try:
            # Get the main database secret
            secret1 = get_secret(secret_arn)
            logger.info("Successfully retrieved main database secret")

            # Check if we need to get credentials from a second secret
            if "creds_secret_manager_arn" in secret1:
                logger.info("Retrieving credentials from secondary secret")
                secret2 = get_secret(secret1["creds_secret_manager_arn"])

                creds = {
                    "host": secret1["server"],
                    "port": int(secret1["port"]),
                    "user": secret2["username"],
                    "password": secret2["password"],
                    "database": secret1["database"]
                }
            else:
                # Direct credentials in the main secret
                creds = {
                    "host": secret1.get("host", secret1.get("server")),
                    "port": int(secret1.get("port", 3306)),
                    "user": secret1.get("username", secret1.get("user")),
                    "password": secret1.get("password"),
                    "database": secret1.get("database", secret1.get("dbname"))
                }

            logger.info(f"Successfully configured database credentials for host: {creds['host']}")
            return creds

        except Exception as e:
            logger.error(f"Error retrieving database credentials from Secrets Manager: {str(e)}")
            raise

def get_secret(secret_arn: str, max_retries: int = 3) -> Dict[str, Any]:
    """
    Retrieve a secret from AWS Secrets Manager with retry logic.

    Args:
        secret_arn: The ARN of the secret to retrieve
        max_retries: Maximum number of retry attempts

    Returns:
        Dictionary containing the secret data
    """
    if not secret_arn:
        raise ValueError("Secret ARN cannot be empty")

    env = os.getenv('ENV', 'local')

    # Create the Secrets Manager client
    if env == 'local':
        client = boto3.client(
            service_name='secretsmanager',
            aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
            aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
            region_name=os.getenv("AWS_REGION", "us-east-1")
        )
    else:
        # In production, use IAM roles
        client = boto3.client(
            service_name='secretsmanager',
            region_name=os.getenv("AWS_REGION", "us-east-1")
        )

    # Retry logic for retrieving secrets
    for attempt in range(max_retries):
        try:
            logger.info(f"Attempting to retrieve secret {secret_arn} (attempt {attempt + 1}/{max_retries})")

            response = client.get_secret_value(SecretId=secret_arn)
            secret_string = response['SecretString']
            secret_data = json.loads(secret_string)

            logger.info(f"Successfully retrieved secret {secret_arn}")
            return secret_data

        except client.exceptions.ResourceNotFoundException:
            logger.error(f"Secret {secret_arn} not found")
            raise
        except client.exceptions.InvalidRequestException:
            logger.error(f"Invalid request for secret {secret_arn}")
            raise
        except client.exceptions.InvalidParameterException:
            logger.error(f"Invalid parameter for secret {secret_arn}")
            raise
        except Exception as e:
            logger.warning(f"Attempt {attempt + 1} failed to retrieve secret {secret_arn}: {str(e)}")

            if attempt < max_retries - 1:
                # Wait before retrying (exponential backoff)
                wait_time = 2 ** attempt
                logger.info(f"Waiting {wait_time} seconds before retry...")
                time.sleep(wait_time)
            else:
                logger.error(f"Failed to retrieve secret {secret_arn} after {max_retries} attempts")
                raise
