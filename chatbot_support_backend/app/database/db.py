"""
Database connection module for the RAG chatbot.
"""

import os
import logging
import time
from typing import Optional
from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import OperationalError, DatabaseError
from app.utils import secrets_manager as sm

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global variables for lazy initialization
engine: Optional[object] = None
SessionLocal: Optional[object] = None

def get_database_url() -> str:
    """
    Get the database URL with proper error handling.

    Returns:
        Database connection URL string
    """
    try:
        db_creds = sm.get_db_credentials()
        database_url = f"mysql+pymysql://{db_creds['user']}:{db_creds['password']}@{db_creds['host']}:{db_creds['port']}/{db_creds['database']}"
        logger.info(f"Database URL configured for host: {db_creds['host']}:{db_creds['port']}")
        return database_url
    except Exception as e:
        logger.error(f"Error getting database credentials: {str(e)}")
        raise

def create_database_engine(max_retries: int = 5):
    """
    Create database engine with retry logic and proper configuration.

    Args:
        max_retries: Maximum number of connection attempts

    Returns:
        SQLAlchemy engine instance
    """
    database_url = get_database_url()
    database_logging = os.getenv("DATABASE_LOGGING", "false").lower() == "true"

    # Engine configuration optimized for AWS RDS
    engine_config = {
        "pool_pre_ping": True,  # Validate connections before use
        "pool_recycle": 3600,   # Recycle connections after 1 hour
        "pool_timeout": 30,     # Timeout for getting connection from pool
        "max_overflow": 10,     # Maximum overflow connections
        "pool_size": 5,         # Base pool size
        "echo": database_logging,  # SQL query logging
        "connect_args": {
            "connect_timeout": 60,  # Connection timeout in seconds
            "read_timeout": 60,     # Read timeout in seconds
            "write_timeout": 60,    # Write timeout in seconds
        }
    }

    for attempt in range(max_retries):
        try:
            logger.info(f"Creating database engine (attempt {attempt + 1}/{max_retries})")

            # Create the engine
            db_engine = create_engine(database_url, **engine_config)

            # Test the connection
            logger.info("Testing database connection...")
            with db_engine.connect() as connection:
                result = connection.execute(text("SELECT 1"))
                result.fetchone()

            logger.info("Database connection test successful")
            return db_engine

        except OperationalError as e:
            logger.warning(f"Database connection attempt {attempt + 1} failed: {str(e)}")

            if attempt < max_retries - 1:
                # Exponential backoff with jitter
                wait_time = min(2 ** attempt + (attempt * 0.1), 30)
                logger.info(f"Waiting {wait_time:.1f} seconds before retry...")
                time.sleep(wait_time)
            else:
                logger.error(f"Failed to connect to database after {max_retries} attempts")
                raise
        except Exception as e:
            logger.error(f"Unexpected error creating database engine: {str(e)}")
            raise

def get_engine():
    """
    Get or create the database engine (lazy initialization).

    Returns:
        SQLAlchemy engine instance
    """
    global engine
    if engine is None:
        logger.info("Initializing database engine...")
        engine = create_database_engine()
    return engine

def get_session_factory():
    """
    Get or create the session factory (lazy initialization).

    Returns:
        SQLAlchemy session factory
    """
    global SessionLocal
    if SessionLocal is None:
        logger.info("Creating database session factory...")
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=get_engine())
    return SessionLocal

# Create base class for models
Base = declarative_base()

class DatabaseSession:
    """
    Database session context manager with improved error handling.
    """

    def __init__(self):
        self.db = None

    def __enter__(self):
        try:
            session_factory = get_session_factory()
            self.db = session_factory()
            return self.db
        except Exception as e:
            logger.error(f"Error creating database session: {str(e)}")
            raise

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.db:
            try:
                if exc_type is not None:
                    logger.error(f"Error in database session: {str(exc_val)}")
                    import traceback
                    logger.error(f"Traceback: {traceback.format_exc()}")
                    self.db.rollback()
                else:
                    # Commit if no exceptions occurred
                    self.db.commit()
            except Exception as e:
                logger.error(f"Error during session cleanup: {str(e)}")
                try:
                    self.db.rollback()
                except:
                    pass
            finally:
                self.db.close()

def get_db():
    """
    Get a database session as a context manager.

    Usage:
        with get_db() as db:
            # Use db here

    Returns:
        A SQLAlchemy session context manager
    """
    return DatabaseSession()

def test_database_connection(max_retries: int = 3) -> bool:
    """
    Test database connectivity with retry logic.

    Args:
        max_retries: Maximum number of test attempts

    Returns:
        True if connection successful, False otherwise
    """
    for attempt in range(max_retries):
        try:
            logger.info(f"Testing database connection (attempt {attempt + 1}/{max_retries})")

            with get_db() as db:
                result = db.execute(text("SELECT 1 as test"))
                test_value = result.fetchone()

                if test_value and test_value[0] == 1:
                    logger.info("Database connection test successful")
                    return True
                else:
                    logger.warning("Database connection test returned unexpected result")

        except Exception as e:
            logger.warning(f"Database connection test attempt {attempt + 1} failed: {str(e)}")

            if attempt < max_retries - 1:
                wait_time = 2 ** attempt
                logger.info(f"Waiting {wait_time} seconds before retry...")
                time.sleep(wait_time)

    logger.error(f"Database connection test failed after {max_retries} attempts")
    return False

def wait_for_database(max_wait_time: int = 300, check_interval: int = 5) -> bool:
    """
    Wait for database to become available.

    Args:
        max_wait_time: Maximum time to wait in seconds
        check_interval: Time between checks in seconds

    Returns:
        True if database becomes available, False if timeout
    """
    logger.info(f"Waiting for database to be ready (max wait: {max_wait_time}s)...")

    start_time = time.time()
    while time.time() - start_time < max_wait_time:
        if test_database_connection(max_retries=1):
            elapsed_time = time.time() - start_time
            logger.info(f"Database is ready after {elapsed_time:.1f} seconds")
            return True

        logger.info(f"Database not ready yet, waiting {check_interval} seconds...")
        time.sleep(check_interval)

    logger.error(f"Database did not become ready within {max_wait_time} seconds")
    return False

def init_db():
    """
    Initialize the database by creating all tables with proper error handling.
    """
    try:
        logger.info("Starting database initialization...")

        # Wait for database to be ready
        if not wait_for_database():
            raise Exception("Database is not available for initialization")

        # Import all models to ensure they are registered with Base
        from .models import (
            User, Group, UserGroup, Document, DocumentAccess,
            Embedding, Conversation, Message, ChatbotConfig,
            Notification, NotificationReadStatus, UserActivity,
            ServiceNameOption, SoftwareMenuOption, IssueTypeOption,
            ProcessedDocument
        )

        # Get the engine
        db_engine = get_engine()

        # Create tables
        logger.info("Creating database tables...")
        Base.metadata.create_all(bind=db_engine)
        logger.info("Database tables created successfully")

        # Test the connection one more time
        if test_database_connection():
            logger.info("Database initialization completed successfully")
        else:
            raise Exception("Database connection test failed after initialization")

    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        raise
