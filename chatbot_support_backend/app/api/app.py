"""
FastAPI application for the RAG chatbot.
"""

import os
import json
import logging
import hashlib
from typing import List, Dict, Any, Optional, Union, Literal
from pathlib import Path
from datetime import datetime, timedelta

from fastapi import FastAPI, HTTPException, UploadFile, File, Form, BackgroundTasks, Depends, Header, <PERSON>ie, Request, status
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON>A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, OAuth2PasswordRequestForm
from jose import JWTError, jwt
from pydantic import BaseModel, Field
import uvicorn

from app.chatbot import RAGChatbot
from app.retrieval.retriever import DocumentRetriever
from app.auth import UserManager, ROLE_ADMIN, ROLE_USER
from app.storage.chatbot_storage import ChatbotStorage
from app.storage.s3 import S3Storage
from app.database.db import get_db
from app.database.models import Document as DBDocument, Embedding as DBEmbedding, DocumentAccess, Group as DBGroup
from app.database.models import ServiceNameOption, SoftwareMenuOption, IssueTypeOption
from app.api.hard_reset import hard_reset_system
from app.api.document_url import generate_document_url
from app.api.dropdown_options import router as dropdown_options_router
from app.services.notification_service import NotificationService, ActivityService
from app.utils.document_number import get_unique_document_number
from app.api.auth import get_current_user, get_current_admin, create_access_token, ACCESS_TOKEN_EXPIRE_MINUTES

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Import database modules
from app.database.db import init_db

# Initialize FastAPI app
app = FastAPI(
    title="RAG Chatbot API",
    description="""API for a Retrieval-Augmented Generation chatbot for PDF documents.

    ## Features

    - **Data Ingestion**: Process PDF documents from a directory or upload individual files
    - **Retrieval-Augmented Generation**: Generate responses based on the content of the documents
    - **Multiple AI Providers**: Support for both OpenAI and Google AI (Gemini) models
    - **Conversation Memory**: Maintain context across multiple interactions

    ## Getting Started

    1. Initialize the chatbot with `/init`
    2. Process documents with `/process-data-folder`, `/ingest`, or `/upload`
    3. Chat with the bot using `/chat`
    4. Save the vector store with `/save` when you're done

    ## API Key Requirements

    - For OpenAI: Set the `OPENAI_API_KEY` environment variable or provide it in the request
    - For Google AI: Set the `GOOGLE_API_KEY` environment variable or provide it in the request
    """,
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    swagger_ui_parameters={"defaultModelsExpandDepth": -1}
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Include dropdown options router
app.include_router(dropdown_options_router, prefix="/api/dropdown-options", tags=["Dropdown Options"])

# Initialize database on startup
@app.on_event("startup")
async def startup_event():
    """
    Application startup event handler with improved database initialization.
    """
    try:
        logger.info("Starting application initialization...")

        # Initialize database with proper error handling and retry logic
        init_db()
        logger.info("Application startup completed successfully")

    except Exception as e:
        logger.error(f"Critical error during application startup: {str(e)}")
        # Log the full traceback for debugging
        import traceback
        logger.error(f"Startup error traceback: {traceback.format_exc()}")

        # In production, you might want to exit the application if database initialization fails
        # For now, we'll log the error and continue, but the app may not function properly
        logger.warning("Application started with database initialization errors - some features may not work")

# Load environment variables
def get_env_var(var_name, default_value):
    """Get environment variable with a default value"""
    return os.environ.get(var_name, default_value)

# Global variables
USER_DATA_DIR = Path(get_env_var("USER_DATA_DIR", "user_data"))
USER_DATA_DIR.mkdir(exist_ok=True)

# Centralized vector store directory
CENTRAL_VECTOR_STORE_DIR = Path(get_env_var("VECTOR_STORE_DIR", "central_vector_store"))
CENTRAL_VECTOR_STORE_DIR.mkdir(exist_ok=True)

# Database configuration
DB_HOST = get_env_var("DB_HOST", "localhost")
DB_PORT = get_env_var("DB_PORT", "3306")
DB_USER = get_env_var("DB_USER", "root")
DB_PASSWORD = get_env_var("DB_PASSWORD", "password")
DB_NAME = get_env_var("DB_NAME", "chatbot")

# Google AI settings
# To use Google AI, you need to set either GOOGLE_API_KEY or GOOGLE_SERVICE_ACCOUNT_FILE environment variable
GOOGLE_SERVICE_ACCOUNT_FILE = get_env_var("GOOGLE_SERVICE_ACCOUNT_FILE", None)
GOOGLE_PROJECT_ID = get_env_var("GOOGLE_PROJECT_ID", None)

# JWT settings
JWT_SECRET_KEY = get_env_var("JWT_SECRET_KEY", "your-secret-key-for-jwt-tokens")
JWT_ALGORITHM = get_env_var("JWT_ALGORITHM", "HS256")
JWT_ACCESS_TOKEN_EXPIRE_MINUTES = int(get_env_var("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

# API settings
CORS_ORIGINS = get_env_var("CORS_ORIGINS", "*").split(",")

# Check if the Google credentials file exists and set it properly
if GOOGLE_SERVICE_ACCOUNT_FILE:
    # Check if the file exists at the specified path
    if os.path.exists(GOOGLE_SERVICE_ACCOUNT_FILE):
        logger.info(f"Google service account file found at {GOOGLE_SERVICE_ACCOUNT_FILE}")
        # Set the environment variable for Google API
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = GOOGLE_SERVICE_ACCOUNT_FILE
    else:
        # Try to find the file in the current directory
        current_dir = os.path.dirname(os.path.abspath(__file__))
        base_dir = os.path.dirname(os.path.dirname(current_dir))
        possible_paths = [
            os.path.join(base_dir, "google_credentials.json"),
            os.path.join(base_dir, "chatbot_support", "google_credentials.json"),
            "/app/google_credentials.json"
        ]

        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Google service account file found at alternative path: {path}")
                os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = path
                GOOGLE_SERVICE_ACCOUNT_FILE = path
                break
        else:
            logger.error(f"Google service account file not found at {GOOGLE_SERVICE_ACCOUNT_FILE} or any alternative paths")
elif os.environ.get("GOOGLE_API_KEY"):
    logger.info("Using Google API key from environment")
else:
    logger.warning("No Google API credentials found. Please set GOOGLE_SERVICE_ACCOUNT_FILE or GOOGLE_API_KEY environment variable.")

# Create singleton instances of storage classes
chatbot_storage = ChatbotStorage()
s3_storage = S3Storage()

# Dictionary to track processed documents
processed_documents: Dict[str, Dict[str, datetime]] = {}

# User manager
user_manager = UserManager()

# Pydantic models for authentication
class Token(BaseModel):
    access_token: str
    token_type: str
    user_id: str
    username: str
    role: str

class UserCreate(BaseModel):
    username: str = Field(..., description="Username")
    password: str = Field(..., description="Password")
    role: str = Field(ROLE_USER, description="User role (admin or user)")
    groups: List[str] = Field([], description="List of group IDs")

class UserUpdate(BaseModel):
    username: Optional[str] = Field(None, description="Username")
    password: Optional[str] = Field(None, description="Password")
    role: Optional[str] = Field(None, description="User role (admin or user)")
    groups: Optional[List[str]] = Field(None, description="List of group IDs")

class GroupCreate(BaseModel):
    name: str = Field(..., description="Group name")
    description: str = Field("", description="Group description")
    members: List[str] = Field(default_factory=list, description="List of user IDs who are members of this group")

class DocumentAccessUpdate(BaseModel):
    doc_id: str = Field(..., description="Document ID (file path or hash)")
    groups: List[str] = Field(..., description="List of group IDs with access")

class NotificationCreate(BaseModel):
    title: str = Field(..., description="Short title for the notification")
    message: str = Field(..., description="Detailed message")
    type: str = Field(..., description="Type of notification (document_added, document_deleted, etc.)")
    user_id: Optional[str] = Field(None, description="ID of the target user (None for global notifications)")
    document_id: Optional[str] = Field(None, description="ID of the related document (if any)")
    is_global: bool = Field(False, description="Whether this is a global notification for all users")

class NotificationUpdate(BaseModel):
    is_read: bool = Field(..., description="Whether the notification has been read")

# Pydantic models for request/response
class ChatRequest(BaseModel):
    message: str = Field(..., description="User message")

class DocumentReference(BaseModel):
    document_id: str = Field(..., description="Document ID")
    filename: str = Field(..., description="Document filename")
    s3_key: Optional[str] = Field(None, description="S3 key for the document")
    page: Optional[int] = Field(None, description="Page number in the document")
    score: Optional[float] = Field(None, description="Relevance score")
    url: Optional[str] = Field(None, description="Presigned URL to access the document")

class ChatResponse(BaseModel):
    response: str = Field(..., description="Chatbot response text")
    conversation_history: Optional[str] = Field(None, description="Formatted conversation history")
    gemini_history: Optional[str] = Field(None, description="Gemini-formatted conversation history")
    format: str = Field("markdown", description="Format of the response text (markdown or plain)")
    document_references: Optional[List[DocumentReference]] = Field([], description="References to source documents")

class IngestRequest(BaseModel):
    directory_path: str = Field(..., description="Path to directory containing PDF files")
    recursive: bool = Field(True, description="Whether to recursively process subdirectories")
    user_id: str = Field(..., description="Unique identifier for the user")

class IngestResponse(BaseModel):
    status: str = Field(..., description="Status of the ingestion process")
    message: str = Field(..., description="Detailed message")

class ConfigRequest(BaseModel):
    use_openai_embeddings: bool = Field(False, description="Whether to use OpenAI embeddings")
    use_google_embeddings: bool = Field(False, description="Whether to use Google AI embeddings")
    use_google_llm: bool = Field(False, description="Whether to use Google AI LLM")
    openai_api_key: Optional[str] = Field(None, description="OpenAI API key")
    google_api_key: Optional[str] = Field(None, description="Google API key")
    google_service_account_file: Optional[str] = Field(None, description="Path to Google service account JSON file")
    google_project_id: Optional[str] = Field(None, description="Google Cloud project ID")
    embedding_model: str = Field("all-MiniLM-L6-v2", description="Embedding model to use")
    llm_model_name: str = Field("gpt-3.5-turbo", description="LLM model to use")
    vector_store_type: str = Field("chroma", description="Type of vector store")
    vector_store_dir: str = Field("", description="Directory to store vectors (will be auto-generated if empty)")
    use_compression: bool = Field(False, description="Whether to use contextual compression")
    top_k: int = Field(4, description="Number of documents to retrieve")
    max_memory_messages: int = Field(10, description="Maximum number of messages to store in memory")

class ConfigResponse(BaseModel):
    status: str = Field(..., description="Status of the configuration process")
    message: str = Field(..., description="Detailed message")
    config: Dict[str, Any] = Field(..., description="Current configuration")

# Function to get vector store directory
def get_vector_store_dir() -> str:
    """Get the centralized vector store directory"""
    return str(CENTRAL_VECTOR_STORE_DIR)

# Function to update all chatbots with new document
def update_all_chatbots_with_document(document_id: str, document_path: str, s3_key: Optional[str] = None):
    """
    Update all chatbots with a new document.

    This function:
    1. Processes the document for all users who have access to it
    2. Updates their vector stores
    3. Reinitializes their retrievers

    Args:
        document_id: The ID of the document in the database
        document_path: Path to the document file
        s3_key: Optional S3 key if the document is stored in S3
    """
    logger.info(f"Updating all chatbots with document {document_id} at {document_path}")

    try:
        # Get all users who have access to this document
        with get_db() as db:
            # Get all groups with access to this document
            groups_with_access = db.query(DocumentAccess.group_id).filter(
                DocumentAccess.document_id == document_id
            ).all()

            # Extract group IDs
            group_ids = [g[0] for g in groups_with_access]

            if not group_ids:
                logger.warning(f"No groups have access to document {document_id}, skipping chatbot updates")
                return

            logger.info(f"Groups with access to document {document_id}: {group_ids}")

            # Get all users in these groups
            from sqlalchemy import or_
            from app.database.models import UserGroup

            users_with_access = db.query(UserGroup.user_id).filter(
                UserGroup.group_id.in_(group_ids)
            ).distinct().all()

            # Extract user IDs
            user_ids = [u[0] for u in users_with_access]

            if not user_ids:
                logger.warning(f"No users have access to document {document_id}, skipping chatbot updates")
                return

            logger.info(f"Users with access to document {document_id}: {user_ids}")

            # Update each user's chatbot
            for user_id in user_ids:
                try:
                    # Get the user's chatbot
                    chatbot = chatbot_storage.get(user_id)

                    if not chatbot:
                        logger.warning(f"Chatbot not found for user {user_id}, skipping update")
                        continue

                    logger.info(f"Processing document for user {user_id}")

                    # Process the document
                    documents = chatbot.pdf_processor.process_pdf(
                        str(document_path),
                        s3_key=s3_key
                    )

                    if not documents:
                        logger.warning(f"No content extracted from document for user {user_id}")
                        continue

                    # Add document ID to metadata
                    for doc in documents:
                        doc.metadata["document_id"] = document_id
                        # Ensure filename is in metadata
                        doc.metadata["file_name"] = os.path.basename(document_path)
                        # Ensure S3 key is in metadata
                        if s3_key:
                            doc.metadata["s3_key"] = s3_key

                    # Add documents to vector store
                    chatbot.vector_store.add_documents(documents)

                    # Save the vector store to ensure persistence
                    if chatbot.vector_store.store_type == "chroma" and chatbot.vector_store.persist_directory:
                        logger.info(f"Saving vector store for user {user_id} to {chatbot.vector_store.persist_directory}")
                        chatbot.vector_store.save(chatbot.vector_store.persist_directory)

                    # Reinitialize retriever with user_id for access control
                    chatbot.retriever = DocumentRetriever(
                        vector_store=chatbot.vector_store,
                        use_compression=chatbot.use_compression,
                        top_k=chatbot.top_k,
                        user_id=user_id  # Pass user_id for access control
                    )

                    # Update the chatbot in storage to ensure the changes persist
                    chatbot_storage.update(user_id, chatbot)

                    logger.info(f"Successfully updated chatbot for user {user_id} with document {document_id}")
                except Exception as e:
                    logger.error(f"Error updating chatbot for user {user_id}: {str(e)}")
    except Exception as e:
        logger.error(f"Error updating chatbots with document {document_id}: {str(e)}")

# Function to calculate document hash
def calculate_document_hash(file_path: str) -> str:
    """Calculate a hash for a document to track if it's been processed"""
    try:
        with open(file_path, "rb") as f:
            file_hash = hashlib.md5(f.read()).hexdigest()
        return file_hash
    except Exception as e:
        logger.error(f"Error calculating document hash: {str(e)}")
        return ""

# Function to reset a corrupted vector store
async def reset_corrupted_vector_store(user_id: str) -> bool:
    """
    Reset a corrupted vector store for a user.

    This function:
    1. Deletes the vector store directory
    2. Creates a new empty vector store

    Args:
        user_id: The user ID

    Returns:
        True if the reset was successful, False otherwise
    """
    try:
        # Use the centralized vector store
        vector_store_dir = get_vector_store_dir()
        logger.info(f"Resetting corrupted vector store at {vector_store_dir} for user {user_id}")

        # Delete the vector store directory if it exists
        if os.path.exists(vector_store_dir):
            import shutil
            shutil.rmtree(vector_store_dir)
            logger.info(f"Deleted corrupted vector store directory: {vector_store_dir}")

        # Create the directory structure
        os.makedirs(vector_store_dir, exist_ok=True)
        logger.info(f"Created new vector store directory: {vector_store_dir}")

        # Create a new empty vector store
        from langchain_community.vectorstores import Chroma
        from app.embedding.embedder import Embedder

        # Initialize embeddings with HuggingFace
        embedder = Embedder(
            model_name="all-MiniLM-L6-v2"
        )

        # Create a new empty Chroma vector store
        Chroma(
            embedding_function=embedder.embeddings,
            persist_directory=vector_store_dir
        )
        logger.info(f"Created new empty Chroma vector store at {vector_store_dir}")

        # Clear any chatbot instances for this user from memory
        chatbot_storage.remove(user_id)
        logger.info(f"Removed chatbot for user {user_id} from memory")

        return True
    except Exception as e:
        logger.error(f"Error resetting corrupted vector store for user {user_id}: {str(e)}")
        return False

# Dependency to ensure chatbot is initialized for a specific user
def get_chatbot(user_id: str):
    logger.info(f"Getting chatbot for user ID: {user_id}")

    # Get the chatbot from the storage
    chatbot = chatbot_storage.get(user_id)

    # If the chatbot is not found, automatically initialize it
    if chatbot is None:
        logger.info(f"Chatbot not found for user ID: {user_id}, automatically initializing")
        try:
            # Use the centralized vector store
            vector_store_dir = get_vector_store_dir()
            logger.info(f"Using centralized vector store directory: {vector_store_dir} for user {user_id}")

            # Create a new chatbot with default settings
            chatbot = RAGChatbot(
                use_google_embeddings=False,  # Use HuggingFace embeddings
                use_google_llm=True,
                google_service_account_file=GOOGLE_SERVICE_ACCOUNT_FILE,
                google_project_id=GOOGLE_PROJECT_ID,
                vector_store_type="chroma",
                vector_store_dir=vector_store_dir,
                user_id=user_id  # Pass user_id for access control
            )

            # Store the chatbot with default config
            config_dict = {
                "use_openai_embeddings": False,
                "use_google_embeddings": False,
                "use_google_llm": True,
                "google_service_account_file": GOOGLE_SERVICE_ACCOUNT_FILE,
                "google_project_id": GOOGLE_PROJECT_ID,
                "embedding_model": "all-MiniLM-L6-v2",
                "llm_model_name": "gemini-pro",
                "vector_store_type": "chroma",
                "vector_store_dir": vector_store_dir,
                "use_compression": False,
                "top_k": 4,
                "max_memory_messages": 10
            }

            chatbot_storage.set(user_id, chatbot, config_dict)
            logger.info(f"Automatically initialized chatbot for user {user_id}")
        except Exception as e:
            logger.error(f"Error automatically initializing chatbot for user {user_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error initializing chatbot: {str(e)}")

    # Check if the retriever is initialized
    if chatbot.retriever is None:
        logger.warning(f"Retriever not initialized for user {user_id}, attempting to initialize it")
        try:
            # Check if there are any embeddings in the database
            from app.database.models import Embedding as DBEmbedding

            with get_db() as db:
                # Get all documents the user has access to
                accessible_docs = user_manager.get_accessible_documents(user_id)

                if accessible_docs:
                    # Check if there are any embeddings for these documents
                    embedding_count = db.query(DBEmbedding).filter(DBEmbedding.document_id.in_(accessible_docs)).count()

                    if embedding_count > 0:
                        logger.info(f"Found {embedding_count} embeddings for user {user_id}, initializing retriever")

                        # Initialize the retriever with user_id for access control
                        from app.retrieval.retriever import DocumentRetriever
                        chatbot.retriever = DocumentRetriever(
                            vector_store=chatbot.vector_store,
                            use_compression=chatbot.use_compression,
                            top_k=chatbot.top_k,
                            user_id=user_id  # Pass user_id for access control
                        )
                        logger.info(f"Successfully initialized retriever for user {user_id}")
                    else:
                        logger.warning(f"No embeddings found for user {user_id}")
                else:
                    logger.warning(f"No accessible documents found for user {user_id}")
        except Exception as e:
            logger.error(f"Error initializing retriever for user {user_id}: {str(e)}")

    logger.info(f"Successfully retrieved chatbot for user ID: {user_id}")
    return chatbot

# Authentication routes
@app.post("/token", response_model=Token, tags=["Authentication"], summary="Login to get access token")
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    # Log the login attempt
    logger.info(f"Login attempt for user: {form_data.username}")

    user_id = user_manager.authenticate(form_data.username, form_data.password)
    if not user_id:
        logger.warning(f"Failed login attempt for user: {form_data.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Log successful authentication
    logger.info(f"User authenticated successfully: {form_data.username} with ID {user_id}")

    user = user_manager.get_user(user_id)

    if not user:
        logger.error(f"User data not found for authenticated user: {form_data.username} with ID {user_id}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User data not found",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user_id, "username": user["username"], "role": user["role"]},
        expires_delta=access_token_expires
    )

    # Log token creation
    logger.info(f"Access token created for user: {form_data.username} with ID {user_id}")

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_id": user_id,
        "username": user["username"],
        "role": user["role"]
    }

@app.post("/register", response_model=Token, tags=["Authentication"], summary="Register a new user")
async def register_user(user: UserCreate):
    # Check if username already exists
    existing_user = user_manager.get_user_by_username(user.username)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered"
        )

    # Only admins can create admin users
    if user.role == ROLE_ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Cannot create admin user through registration"
        )

    # Create user
    user_id = user_manager.create_user(
        username=user.username,
        password=user.password,
        role=ROLE_USER,  # Force regular user role for registration
        groups=[]
    )

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user"
        )

    # Create access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user_id, "username": user.username, "role": ROLE_USER},
        expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_id": user_id,
        "username": user.username,
        "role": ROLE_USER
    }

# User management routes (admin only)
@app.get("/users", tags=["User Management"], summary="Get all users")
async def get_users(current_user: dict = Depends(get_current_admin)):
    return {"users": user_manager.get_all_users()}

@app.get("/users/me/details", tags=["User Management"], summary="Get current user details including groups")
async def get_current_user_details(current_user: dict = Depends(get_current_user)):
    """
    Get details of the currently authenticated user, including their groups.

    This endpoint allows regular users to access their own user details,
    including which groups they belong to.

    Returns:
        The user data including groups
    """
    user_id = current_user["id"]
    user = user_manager.get_user(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    return {"user": user}

@app.get("/users/{user_id}", tags=["User Management"], summary="Get user by ID")
async def get_user(user_id: str, current_user: dict = Depends(get_current_admin)):
    user = user_manager.get_user(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    return {"user": user}

@app.post("/users", tags=["User Management"], summary="Create a new user (admin only)")
async def create_user(user: UserCreate, current_user: dict = Depends(get_current_admin)):
    # Check if username already exists
    existing_user = user_manager.get_user_by_username(user.username)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered"
        )

    # Create user
    user_id = user_manager.create_user(
        username=user.username,
        password=user.password,
        role=user.role,
        groups=user.groups
    )

    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user"
        )

    return {"user_id": user_id, "message": "User created successfully"}

@app.put("/users/{user_id}", tags=["User Management"], summary="Update a user (admin only)")
async def update_user(user_id: str, user: UserUpdate, current_user: dict = Depends(get_current_admin)):
    # Check if user exists
    if not user_manager.get_user(user_id):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Update user
    update_data = {k: v for k, v in user.dict().items() if v is not None}
    success = user_manager.update_user(user_id, update_data)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user"
        )

    return {"message": "User updated successfully"}

@app.delete("/users/{user_id}", tags=["User Management"], summary="Delete a user (admin only)")
async def delete_user(user_id: str, current_user: dict = Depends(get_current_admin)):
    # Check if user exists
    if not user_manager.get_user(user_id):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Delete user
    success = user_manager.delete_user(user_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete user"
        )

    return {"message": "User deleted successfully"}

# Group management routes (admin only)
@app.get("/groups", tags=["Group Management"], summary="Get all groups")
async def get_groups(current_user: dict = Depends(get_current_admin)):
    return {"groups": user_manager.get_all_groups()}

@app.post("/groups", tags=["Group Management"], summary="Create a new group (admin only)")
async def create_group(group: GroupCreate, current_user: dict = Depends(get_current_admin)):
    try:
        # Create the group
        group_id = user_manager.create_group(name=group.name, description=group.description)

        if not group_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create group"
            )

        # Add members to the group if provided
        if group.members:
            for member_id in group.members:
                success = user_manager.add_user_to_group(member_id, group_id)
                if not success:
                    logger.warning(f"Failed to add user {member_id} to group {group_id}")

        return {"group_id": group_id, "message": "Group created successfully"}
    except Exception as e:
        logger.error(f"Error creating group: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create group: {str(e)}"
        )

@app.put("/groups/{group_id}", tags=["Group Management"], summary="Update a group (admin only)")
async def update_group(group_id: str, group: GroupCreate, current_user: dict = Depends(get_current_admin)):
    """
    Update an existing group.

    Args:
        group_id: The ID of the group to update
        group: The updated group data
        current_user: The current admin user

    Returns:
        Success message
    """
    try:
        # Check if group exists
        existing_group = user_manager.get_group(group_id)
        if not existing_group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Group not found"
            )

        # Update group name and description
        update_data = {
            "name": group.name,
            "description": group.description
        }

        success = user_manager.update_group(group_id, update_data)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update group"
            )

        # Get current members
        current_members = existing_group.get("members", [])

        # Remove members that are not in the new list
        for member_id in list(current_members):  # Create a copy to avoid modification during iteration
            if member_id not in group.members:
                user_manager.remove_user_from_group(member_id, group_id)

        # Add new members
        for member_id in group.members:
            if member_id not in current_members:
                user_manager.add_user_to_group(member_id, group_id)

        return {"message": "Group updated successfully", "group_id": group_id}
    except Exception as e:
        logger.error(f"Error updating group {group_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update group: {str(e)}"
        )

@app.delete("/groups/{group_id}", tags=["Group Management"], summary="Delete a group (admin only)")
async def delete_group(group_id: str, current_user: dict = Depends(get_current_admin)):
    # Check if group exists
    if not user_manager.get_group(group_id):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )

    # Delete group
    success = user_manager.delete_group(group_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete group"
        )

    return {"message": "Group deleted successfully"}

@app.post("/groups/{group_id}/users/{user_id}", tags=["Group Management"], summary="Add a user to a group (admin only)")
async def add_user_to_group(group_id: str, user_id: str, current_user: dict = Depends(get_current_admin)):
    # Check if group and user exist
    if not user_manager.get_group(group_id):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )

    if not user_manager.get_user(user_id):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Add user to group
    success = user_manager.add_user_to_group(user_id, group_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add user to group"
        )

    return {"message": "User added to group successfully"}

@app.delete("/groups/{group_id}/users/{user_id}", tags=["Group Management"], summary="Remove a user from a group (admin only)")
async def remove_user_from_group(group_id: str, user_id: str, current_user: dict = Depends(get_current_admin)):
    # Check if group and user exist
    if not user_manager.get_group(group_id):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Group not found"
        )

    if not user_manager.get_user(user_id):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Remove user from group
    success = user_manager.remove_user_from_group(user_id, group_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove user from group"
        )

    return {"message": "User removed from group successfully"}

# Document access routes (admin only)
@app.post("/document-access", tags=["Document Access"], summary="Set document access for groups (admin only)")
async def set_document_access(access: DocumentAccessUpdate, current_user: dict = Depends(get_current_admin)):
    # Check if document exists
    with get_db() as db:
        doc = db.query(DBDocument).filter(DBDocument.id == access.doc_id).first()
        if not doc:
            # Try to find by filename
            doc = db.query(DBDocument).filter(DBDocument.filename == access.doc_id).first()
            if not doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Document with ID or filename {access.doc_id} not found"
                )
            # Use the document ID instead of filename
            doc_id = doc.id
            logger.info(f"Found document by filename {access.doc_id}, using ID {doc_id}")
        else:
            doc_id = access.doc_id

    # Set document access
    success = user_manager.set_document_access(doc_id, access.groups)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to set document access"
        )

    return {"message": "Document access set successfully"}

@app.get("/document-access/{doc_id}", tags=["Document Access"], summary="Get document access for a document")
async def get_document_access(doc_id: str, current_user: dict = Depends(get_current_user)):
    # Check if document exists
    with get_db() as db:
        doc = db.query(DBDocument).filter(DBDocument.id == doc_id).first()
        if not doc:
            # Try to find by filename
            doc = db.query(DBDocument).filter(DBDocument.filename == doc_id).first()
            if not doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Document with ID or filename {doc_id} not found"
                )
            # Use the document ID instead of filename
            real_doc_id = doc.id
            logger.info(f"Found document by filename {doc_id}, using ID {real_doc_id}")
        else:
            real_doc_id = doc_id

    # Admins can see access for any document
    if user_manager.is_admin(current_user["id"]):
        groups = user_manager.get_document_access(real_doc_id)
        return {"doc_id": doc_id, "real_doc_id": real_doc_id, "groups": groups}

    # Regular users can only check if they have access
    has_access = user_manager.user_has_document_access(current_user["id"], real_doc_id)
    return {"doc_id": doc_id, "real_doc_id": real_doc_id, "has_access": has_access}

@app.get("/document-url/{doc_id}", tags=["Document Access"], summary="Get a presigned URL for a document")
async def get_document_url(doc_id: str, expiration: int = 3600, current_user: dict = Depends(get_current_user)):
    """
    Generate a presigned URL for a document.

    This endpoint generates a temporary URL that can be used to access the document directly.
    The URL will expire after the specified time (default: 1 hour).

    Users can only get URLs for documents they have access to.
    Admins can get URLs for any document.

    Args:
        doc_id: The ID of the document
        expiration: URL expiration time in seconds (default: 1 hour)

    Returns:
        Document details and presigned URL
    """
    # Use the authenticated user's ID
    user_id = current_user["id"]

    try:
        # Call the document URL generation function
        result = generate_document_url(
            document_id=doc_id,
            user_id=user_id,
            user_manager=user_manager,
            expiration=expiration
        )

        return result
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error generating document URL: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating document URL: {str(e)}"
        )

# Routes
@app.post("/init", response_model=ConfigResponse, tags=["Setup"], summary="Initialize the chatbot")
async def initialize_chatbot(config: ConfigRequest, current_user: dict = Depends(get_current_user)):
    """
    Initialize the chatbot with the specified configuration.

    This endpoint must be called before using any other endpoints that require the chatbot.
    The chatbot will be initialized for the authenticated user.

    The system uses HuggingFace embeddings for document processing and Google AI for LLM responses:
    - For LLM responses: Set `use_google_llm=true` and either:
      - Provide a Google API key, or
      - Provide a path to a Google service account JSON file with `google_service_account_file`
    - The `use_openai_embeddings` and `use_google_embeddings` parameters are ignored as the system always uses HuggingFace embeddings

    ## Example with Google API Key
    ```json
    {
      "use_google_llm": true,
      "google_api_key": "your_google_api_key"
    }
    ```

    ## Example with Google Service Account
    ```json
    {
      "use_google_llm": true,
      "google_service_account_file": "/app/google_credentials.json"
    }
    ```
    """
    # Use the authenticated user's ID
    user_id = current_user["id"]

    try:
        # Create user directory if it doesn't exist
        user_dir = USER_DATA_DIR / user_id
        user_dir.mkdir(exist_ok=True)

        # Ensure user directory has correct permissions
        try:
            import os
            os.chmod(str(user_dir), 0o777)
            logger.info(f"Set permissions on user directory: {user_dir}")
        except Exception as e:
            logger.warning(f"Could not set permissions on user directory {user_dir}: {str(e)}")

        # Use the centralized vector store directory
        vector_store_dir = config.vector_store_dir
        if not vector_store_dir:
            vector_store_dir = get_vector_store_dir()

        # Ensure vector store directory exists and has correct permissions
        vector_store_path = Path(vector_store_dir)
        vector_store_path.mkdir(exist_ok=True, parents=True)
        try:
            os.chmod(str(vector_store_path), 0o777)
            logger.info(f"Set permissions on vector store directory: {vector_store_path}")
        except Exception as e:
            logger.warning(f"Could not set permissions on vector store directory {vector_store_path}: {str(e)}")

        logger.info(f"Using vector store directory: {vector_store_dir} for user {user_id}")

        # Initialize user's document tracking
        if user_id not in processed_documents:
            processed_documents[user_id] = {}

        # Check if we should load an existing vector store
        vector_store_path = Path(vector_store_dir)
        if vector_store_path.exists() and any(vector_store_path.iterdir()):
            logger.info(f"Loading existing vector store for user {user_id} from {vector_store_dir}")
            chatbot = app.load(
                vector_store_path=vector_store_dir,
                use_openai_embeddings=False,  # Always use HuggingFace embeddings
                use_google_embeddings=False,  # Always use HuggingFace embeddings
                use_google_llm=config.use_google_llm,
                openai_api_key=config.openai_api_key,
                google_api_key=config.google_api_key,
                google_service_account_file=config.google_service_account_file,
                google_project_id=config.google_project_id,
                embedding_model=config.embedding_model,
                llm_model_name=config.llm_model_name,
                vector_store_type=config.vector_store_type,
                use_compression=config.use_compression,
                top_k=config.top_k,
                max_memory_messages=config.max_memory_messages,
                user_id=user_id
            )
            # Store the chatbot in the storage
            config_dict = config.model_dump()
            config_dict["vector_store_dir"] = vector_store_dir
            chatbot_storage.set(user_id, chatbot, config_dict)
            message = f"Chatbot loaded from existing vector store for user {user_id}"
        else:
            # Create a new chatbot instance with HuggingFace embeddings
            chatbot = RAGChatbot(
                use_openai_embeddings=False,  # Always use HuggingFace embeddings
                use_google_embeddings=False,  # Always use HuggingFace embeddings
                use_google_llm=config.use_google_llm,
                openai_api_key=config.openai_api_key,
                google_api_key=config.google_api_key,
                google_service_account_file=config.google_service_account_file,
                google_project_id=config.google_project_id,
                embedding_model=config.embedding_model,
                llm_model_name=config.llm_model_name,
                vector_store_type=config.vector_store_type,
                vector_store_dir=vector_store_dir,
                use_compression=config.use_compression,
                top_k=config.top_k,
                max_memory_messages=config.max_memory_messages,
                user_id=user_id
            )
            # Store the chatbot in the storage
            config_dict = config.model_dump()
            config_dict["vector_store_dir"] = vector_store_dir
            chatbot_storage.set(user_id, chatbot, config_dict)
            message = f"Chatbot initialized successfully for user {user_id}"

        logger.info(message)

        # Update config with actual vector store directory
        config_dict = config.model_dump()
        config_dict["vector_store_dir"] = vector_store_dir

        return ConfigResponse(
            status="success",
            message=message,
            config=config_dict
        )
    except Exception as e:
        logger.error(f"Error initializing chatbot for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error initializing chatbot: {str(e)}")

@app.post("/chat", response_model=ChatResponse, tags=["Interaction"], summary="Chat with the bot")
async def chat(
    request: ChatRequest,
    conversation_id: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """
    Process a user message and generate a response based on the ingested documents.

    The chatbot will retrieve relevant information from the documents and generate a response.
    The conversation history is maintained across multiple interactions.
    Only documents the user has access to will be used for generating responses.

    ## Example
    ```json
    {
      "message": "What information is in the documents?"
    }
    ```

    ## Parameters
    - **message**: The user's message
    - **conversation_id**: Optional ID of the conversation to continue. If not provided, the most recent conversation will be used.

    ## Note
    You must initialize the chatbot with `/init` and ingest documents before using this endpoint.
    """
    # Use the authenticated user's ID
    user_id = current_user["id"]
    logger.info(f"Chat request received from user {user_id} with message: {request.message}")

    try:
        # Get the user's chatbot instance
        logger.info(f"Getting chatbot for user {user_id}")
        chatbot = get_chatbot(user_id)

        # If conversation_id is provided, set it in the chatbot's memory and load messages
        if conversation_id:
            logger.info(f"Using specified conversation ID: {conversation_id}")
            # Verify the conversation exists and belongs to this user
            from app.database.models import Conversation as DBConversation
            from app.database.db import get_db

            with get_db() as db:
                conversation = db.query(DBConversation).filter(
                    DBConversation.id == conversation_id,
                    DBConversation.user_id == user_id
                ).first()

                if not conversation:
                    logger.warning(f"Conversation {conversation_id} not found for user {user_id}")
                    raise HTTPException(status_code=404, detail="Conversation not found")

                # Set the conversation ID in the chatbot's memory
                chatbot.memory.conversation_id = conversation_id
                # Load messages from the database
                chatbot.memory._load_from_database()
                logger.info(f"Loaded conversation {conversation_id} with {len(chatbot.memory.messages)} messages")

        # Process the message
        response_data = chatbot.chat(request.message)

        # Get conversation history in different formats
        default_history = chatbot.get_conversation_history(format_type="default")
        gemini_history = chatbot.get_conversation_history(format_type="gemini")

        # Use the default format for the response
        conversation_history = default_history

        # Check if response is a dictionary or string
        if isinstance(response_data, dict):
            response_text = response_data.get("text", "")
            document_references = response_data.get("document_references", [])
        else:
            # Handle legacy format (string response)
            response_text = response_data
            document_references = []

        # Generate document URLs for any document references
        doc_refs_with_urls = []
        for ref in document_references:
            try:
                # Create a copy of the reference
                ref_with_url = dict(ref)

                # Generate a presigned URL for the document
                if ref.get("document_id"):
                    try:
                        # Call the document URL generation function with page number if available
                        url_result = generate_document_url(
                            document_id=ref["document_id"],
                            user_id=user_id,
                            user_manager=user_manager,
                            expiration=3600,  # 1 hour expiration
                            page=ref.get("page")  # Pass the page number if available
                        )
                        # Add the URL to the reference
                        ref_with_url["url"] = url_result.get("url")
                        logger.info(f"Generated URL for document {ref['document_id']}: {url_result.get('url')[:50]}...")
                    except Exception as url_error:
                        logger.error(f"Error generating URL for document {ref['document_id']}: {str(url_error)}")
                        ref_with_url["url"] = None
                else:
                    logger.warning(f"Document reference missing document_id: {ref}")

                doc_refs_with_urls.append(ref_with_url)
            except Exception as e:
                logger.error(f"Error processing document reference: {str(e)}")

        # Create document reference objects
        document_reference_objects = [
            DocumentReference(
                document_id=ref["document_id"],
                filename=ref["filename"],
                s3_key=ref.get("s3_key"),
                page=ref.get("page"),
                score=ref.get("score"),
                url=ref.get("url")
            ) for ref in doc_refs_with_urls
        ]

        # Log the document references for debugging
        for i, ref in enumerate(document_reference_objects):
            logger.info(f"Document reference {i+1}: id={ref.document_id}, filename={ref.filename}, has_url={ref.url is not None}")

        logger.info(f"Returning response with {len(document_reference_objects)} document references")

        return ChatResponse(
            response=response_text,
            conversation_history=default_history,
            gemini_history=gemini_history,
            format="markdown",  # Explicitly set format to markdown
            document_references=document_reference_objects
        )
    except Exception as e:
        error_str = str(e)
        logger.error(f"Error processing chat for user {user_id}: {error_str}")

        # Check if this is a Chroma collection error
        if "Collection" in error_str and "does not exists" in error_str or "Missing metadata segment" in error_str:
            logger.warning(f"Detected corrupted Chroma collection for user {user_id}, attempting to reset vector store")

            try:
                # Try to reset the vector store
                await reset_corrupted_vector_store(user_id)

                # Return a friendly error message
                raise HTTPException(
                    status_code=500,
                    detail="Your chat data was corrupted and has been reset. Please refresh the page and try again."
                )
            except Exception as reset_error:
                logger.error(f"Error resetting vector store for user {user_id}: {str(reset_error)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Error processing chat: {error_str}. Failed to reset: {str(reset_error)}"
                )

        # If not a Chroma collection error, raise the original exception
        raise HTTPException(status_code=500, detail=f"Error processing chat: {error_str}")

@app.post("/ingest", response_model=IngestResponse, tags=["Document Processing"], summary="Ingest documents from a directory")
async def ingest_documents(
    request: IngestRequest,
    background_tasks: BackgroundTasks
):
    """
    Ingest documents from a specified directory.

    This endpoint processes all PDF files in the specified directory and adds them to the vector store.
    The processing is done in the background, so the endpoint returns immediately.

    ## Example
    ```json
    {
      "directory_path": "data",
      "recursive": true
    }
    ```

    ## Note
    You must initialize the chatbot with `/init` before using this endpoint.
    """
    user_id = request.user_id

    try:
        # Get the user's chatbot instance
        chatbot = get_chatbot(user_id)

        # Check if directory exists
        directory_path = Path(request.directory_path)
        if not directory_path.exists() or not directory_path.is_dir():
            raise HTTPException(status_code=400, detail=f"Directory not found: {request.directory_path}")

        # Check if we've already processed these documents
        pdf_files = list(directory_path.glob("**/*.pdf" if request.recursive else "*.pdf"))

        # Filter out already processed documents
        new_files = []
        for file in pdf_files:
            file_hash = calculate_document_hash(str(file))
            if file_hash and file_hash not in processed_documents[user_id]:
                new_files.append(file)
                # Mark as processed
                processed_documents[user_id][file_hash] = datetime.now()

        if not new_files:
            return IngestResponse(
                status="info",
                message=f"No new documents to process in {request.directory_path}"
            )

        # Start ingestion in background
        background_tasks.add_task(chatbot.ingest_documents, request.directory_path, request.recursive)

        # Save the vector store after processing
        background_tasks.add_task(chatbot.save_vector_store)

        return IngestResponse(
            status="success",
            message=f"Started ingesting {len(new_files)} new documents from {request.directory_path}"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error ingesting documents for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error ingesting documents: {str(e)}")

@app.post("/upload", response_model=IngestResponse, tags=["Document Processing"], summary="Upload and process a PDF file")
async def upload_pdf(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(..., description="The PDF file to upload"),
    groups: str = Form("[]", description="List of group IDs with access to the document (JSON string)"),
    group_ids: List[str] = Form([], description="List of group IDs with access to the document (individual IDs)"),
    service_name: str = Form(None, description="Service name for the document"),
    software_menus: str = Form(None, description="Software menus for the document"),
    issue_type: str = Form(None, description="Issue type for the document"),
    current_user: dict = Depends(get_current_user)  # Allow any authenticated user to upload documents
):
    """
    Upload and process a single PDF file.

    This endpoint allows you to upload a PDF file, which will be processed and added to the vector store.
    The file will be saved in the 'uploads' directory.

    ## Note
    - Only PDF files are supported
    - You must initialize the chatbot with `/init` before using this endpoint
    - The file size is limited to 100MB by default
    """
    # Use the authenticated user's ID
    user_id = current_user["id"]
    is_admin = user_manager.is_admin(user_id)

    # Parse the groups parameter from JSON string to list
    group_list = []

    # First try to parse the groups JSON string
    try:
        # Try to parse the groups as a JSON string
        parsed_groups = json.loads(groups)
        if isinstance(parsed_groups, list):
            group_list.extend(parsed_groups)
        else:
            logger.warning(f"Groups parameter is not a valid list: {groups}")
    except json.JSONDecodeError:
        logger.warning(f"Failed to parse groups parameter as JSON: {groups}")
    except Exception as e:
        logger.warning(f"Error parsing groups parameter: {str(e)}")

    # Then add any individual group_ids
    if group_ids:
        logger.info(f"Adding individual group_ids: {group_ids}")
        group_list.extend(group_ids)

    # Remove duplicates while preserving order
    seen = set()
    groups = [x for x in group_list if not (x in seen or seen.add(x))]

    logger.info(f"Final groups for document upload: {groups}")

    try:
        # Get the user's chatbot instance
        user_chatbot = get_chatbot(user_id)

        # Check if file is a PDF
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="Only PDF files are supported")

        # Create common data directory for temporary storage
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)

        # Save the file temporarily to calculate hash
        file_path = data_dir / file.filename
        file_content = await file.read()
        with open(file_path, "wb") as f:
            f.write(file_content)

        # Calculate file hash
        file_hash = calculate_document_hash(str(file_path))

        # Upload file to S3
        try:
            # Reset file position to beginning
            file.file.seek(0)
            # Upload to S3
            s3_key = s3_storage.upload_file(
                file.file,
                file.filename,
                file.content_type or "application/pdf"
            )
            logger.info(f"Uploaded file {file.filename} to S3 with key {s3_key}")
        except Exception as e:
            logger.error(f"Error uploading file to S3: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error uploading file to S3: {str(e)}")

        # Add the document to the database first
        document_id = None
        with get_db() as db:
            # Check if document already exists
            existing_doc = db.query(DBDocument).filter(DBDocument.filename == file.filename).first()
            if existing_doc:
                document_id = existing_doc.id
                logger.info(f"Document {file.filename} already exists with ID {document_id}")
            else:
                # Generate a unique document number with the new format
                document_number = get_unique_document_number(db, file.filename, prefix="DOC")
                logger.info(f"Generated document number: {document_number} for file {file.filename}")

                # Create a new document record with S3 key, document number, and metadata fields
                document = DBDocument(
                    filename=file.filename,
                    document_number=document_number,  # Add the document number
                    s3_key=s3_key,  # Use the actual S3 key
                    content_type=file.content_type or "application/pdf",
                    size=os.path.getsize(file_path),
                    uploaded_by=user_id,
                    service_name=service_name,
                    software_menus=software_menus,
                    issue_type=issue_type
                )
                db.add(document)
                db.commit()
                document_id = document.id
                logger.info(f"Added document {file.filename} to database with ID {document_id}")

        # Now set document access for the specified groups using the document ID
        if document_id:
            logger.info(f"Setting document access for document ID: {document_id}")

            # If no groups specified and user is not admin, use user's groups
            if not groups and not is_admin:
                user_data = user_manager.get_user(user_id)
                if user_data and "groups" in user_data and user_data["groups"]:
                    groups = user_data["groups"]
                    logger.info(f"Using user's groups for document access: {groups}")

            # For regular users, verify they belong to the groups they're trying to use
            if not is_admin and groups:
                user_data = user_manager.get_user(user_id)
                if user_data and "groups" in user_data:
                    user_groups = set(user_data["groups"])
                    # Filter out groups the user doesn't belong to
                    valid_groups = [g for g in groups if g in user_groups]
                    if len(valid_groups) != len(groups):
                        logger.warning(f"User {user_id} attempted to use groups they don't belong to. Filtered from {groups} to {valid_groups}")
                    groups = valid_groups

            # If admin is uploading and no groups are selected, don't automatically add admin group
            # This allows admins to create documents with no access restrictions
            if is_admin and not groups:
                logger.info(f"Admin user {user_id} uploaded document with no groups selected")
                # Empty groups list means no access restrictions
            else:
                # For non-admin users or when admin selects specific groups
                # Find the Administrators group and add it to the groups list if not already present
                try:
                    with get_db() as db:
                        admin_group = db.query(DBGroup).filter(DBGroup.name == "Administrators").first()
                        if admin_group and admin_group.id not in groups:
                            logger.info(f"Adding Administrators group {admin_group.id} to document access")
                            groups.append(admin_group.id)
                except Exception as e:
                    logger.warning(f"Error finding Administrators group: {str(e)}")

            # Log the final groups that will be used for document access
            logger.info(f"Setting document access for document ID {document_id} with groups: {groups}")

            # Make sure we're using the document ID, not the filename
            success = user_manager.set_document_access(document_id, groups)
            if not success:
                logger.error(f"Failed to set document access for document ID: {document_id}")
                # Try to get the document ID again to make sure it exists
                with get_db() as db:
                    doc = db.query(DBDocument).filter(DBDocument.id == document_id).first()
                    if doc:
                        logger.info(f"Document exists with ID {document_id}, but access setting failed")
                    else:
                        logger.error(f"Document with ID {document_id} does not exist in the database")
        else:
            logger.error(f"Failed to get document ID for {file.filename}")

        # Process the file for the user first - use S3 key if available
        documents = user_chatbot.pdf_processor.process_pdf(
            str(file_path),
            s3_key=s3_key  # Pass the S3 key for future retrieval
        )

        # Add document ID to metadata
        if documents and document_id:
            for doc in documents:
                doc.metadata["document_id"] = document_id
                # Ensure filename is in metadata
                doc.metadata["file_name"] = file.filename
                # Ensure S3 key is in metadata
                doc.metadata["s3_key"] = s3_key

        if not documents:
            return IngestResponse(
                status="warning",
                message=f"No content extracted from {file.filename}"
            )

        logger.info(f"Processed {len(documents)} chunks from {file.filename} with document_id {document_id}")

        # Update document record to mark as processed
        with get_db() as db:
            db_doc = db.query(DBDocument).filter(DBDocument.id == document_id).first()
            if db_doc:
                db_doc.processed = True
                db_doc.processed_at = datetime.now()
                db.commit()
                logger.info(f"Marked document {document_id} as processed in database")

        # Add to user's vector store
        try:
            if user_chatbot.vector_store.vector_store is None:
                logger.info(f"Creating new vector store for user with {len(documents)} chunks")
                user_chatbot.vector_store.create_from_documents(documents)

                # Initialize retriever if not already initialized
                if user_chatbot.retriever is None:
                    user_chatbot.retriever = DocumentRetriever(
                        vector_store=user_chatbot.vector_store,
                        use_compression=user_chatbot.use_compression,
                        top_k=user_chatbot.top_k
                    )
            else:
                logger.info(f"Adding {len(documents)} chunks to existing user vector store")
                user_chatbot.vector_store.add_documents(documents)

            # Mark file as processed for user
            if user_id not in processed_documents:
                processed_documents[user_id] = {}
            processed_documents[user_id][file_hash] = datetime.now()

            # Save the user's vector store
            user_chatbot.save_vector_store()
            logger.info(f"Saved user vector store after processing {file.filename}")
        except Exception as e:
            logger.error(f"Error adding document to admin vector store: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error adding document to vector store: {str(e)}")

        # Use our new document sharing function to update all chatbots with the new document
        if document_id:
            logger.info(f"Updating all chatbots with document {document_id}")
            # Import the document sharing module
            from app.api.document_sharing import ensure_document_in_user_vector_stores

            # Use background task to avoid blocking the response
            background_tasks.add_task(
                ensure_document_in_user_vector_stores,
                document_id=document_id,
                group_ids=groups,
                chatbot_storage=chatbot_storage,
                user_data_dir=USER_DATA_DIR,
                google_service_account_file=GOOGLE_SERVICE_ACCOUNT_FILE,
                google_project_id=GOOGLE_PROJECT_ID
            )

            # Create a notification for all users about the new document
            try:
                # Import the simple notification service
                from app.services.simple_notification import send_notification_to_all_users

                logger.info(f"Creating document upload notification for document {file.filename} (ID: {document_id})")

                # Send a notification to all users
                notification_result = send_notification_to_all_users(
                    title="New Document Available",
                    message=f"A new document '{file.filename}' has been added to the system.",
                    notification_type="document_added",
                    created_by=user_id,
                    document_id=document_id,
                    skip_user_ids=[user_id]  # Skip the user who uploaded the document
                )

                if notification_result:
                    logger.info(f"Successfully sent document upload notification to all users for document {file.filename}")
                else:
                    logger.error(f"Failed to send document upload notification for document {file.filename}")

                # Double-check that notifications were created
                with get_db() as db:
                    from app.database.models import Notification
                    notification_count = db.query(Notification).filter(
                        Notification.type == "document_added",
                        Notification.document_id == document_id
                    ).count()

                    logger.info(f"Found {notification_count} notifications for document {document_id} in the database")
            except Exception as e:
                logger.error(f"Error creating notification for document upload: {str(e)}", exc_info=True)

            # Record activity
            ActivityService.record_activity(
                user_id=user_id,
                action="document_upload",
                document_id=document_id,
                details={
                    "filename": file.filename
                }
            )

            return IngestResponse(
                status="success",
                message=f"Successfully processed {file.filename} with {len(documents)} chunks. All users with access will be updated automatically."
            )
        else:
            return IngestResponse(
                status="warning",
                message=f"Document processed but could not be added to database. Users will not be automatically updated."
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing uploaded file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing uploaded file: {str(e)}")

@app.get("/conversations", tags=["Interaction"], summary="Get user conversations")
async def get_conversations(
    page: int = 1,
    limit: int = 20,
    include_empty: bool = False,
    current_user: dict = Depends(get_current_user)
):
    """
    Get all conversations for the current user with pagination support.

    This endpoint returns a list of conversations for the authenticated user.
    Each conversation includes its ID, creation date, update date, and message count.

    For admin users, this endpoint returns all conversations from all users.

    Args:
        page: Page number (starting from 1)
        limit: Number of items per page
        include_empty: Whether to include conversations with 0 messages (default: False)

    Returns:
        A list of conversation objects
    """
    # Use the authenticated user's ID
    user_id = current_user["id"]
    is_admin = current_user.get("role") == "admin"

    try:
        # Import the ConversationMemory class
        from app.memory.conversation_memory import ConversationMemory
        from app.database.models import Message
        from app.database.db import get_db

        if is_admin:
            # Admin users can see all conversations
            conversations = ConversationMemory.get_all_conversations()
        else:
            # Regular users can only see their own conversations
            conversations = ConversationMemory.get_user_conversations(user_id)

        # Filter out conversations with 0 messages if include_empty is False
        if not include_empty:
            # The message count is already included in the conversation data
            # from the optimized query, so we can filter without additional DB queries
            filtered_conversations = [conv for conv in conversations if conv["message_count"] > 0]
            logger.info(f"Filtered out {len(conversations) - len(filtered_conversations)} empty conversations")
            conversations = filtered_conversations

        # Apply pagination
        # Calculate start and end indices
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit

        # Get the paginated conversations
        paginated_conversations = conversations[start_idx:end_idx]

        # Return the paginated conversations along with total count
        return {
            "conversations": paginated_conversations,
            "total": len(conversations),
            "page": page,
            "limit": limit,
            "pages": (len(conversations) + limit - 1) // limit  # Ceiling division
        }
    except Exception as e:
        logger.error(f"Error getting conversations for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting conversations: {str(e)}")

@app.post("/conversations/new", tags=["Interaction"], summary="Create a new conversation")
async def create_new_conversation(
    title: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new conversation for the current user.

    This endpoint creates a new conversation for the authenticated user
    and returns the new conversation details.

    Args:
        title: Optional title for the conversation

    Returns:
        The newly created conversation object
    """
    # Use the authenticated user's ID
    user_id = current_user["id"]

    try:
        # Import the ConversationMemory class
        from app.memory.conversation_memory import ConversationMemory
        from app.database.models import User as DBUser
        from app.database.db import get_db

        # Verify the user exists in the database
        with get_db() as db:
            user = db.query(DBUser).filter(DBUser.id == user_id).first()
            if not user:
                logger.error(f"User {user_id} not found in database")
                raise HTTPException(status_code=404, detail=f"User not found")

        # Create a new conversation with the provided title
        # This now returns a dictionary directly
        conversation_dict = ConversationMemory.create_conversation(user_id, title)

        # Get the chatbot instance for this user
        chatbot = get_chatbot(user_id)

        # Update the chatbot's conversation ID
        if chatbot and hasattr(chatbot, 'memory'):
            chatbot.memory.conversation_id = conversation_dict["id"]
        else:
            logger.warning(f"Chatbot or chatbot.memory not available for user {user_id}")

        # Use the dictionary directly
        conv_dict = conversation_dict

        return {"conversation": conv_dict}
    except ValueError as e:
        logger.error(f"Value error creating conversation for user {user_id}: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Invalid request: {str(e)}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating new conversation for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating new conversation: {str(e)}")

@app.get("/conversations/recent", tags=["Interaction"], summary="Get most recent conversation")
async def get_most_recent_conversation(current_user: dict = Depends(get_current_user)):
    """
    Get the most recent conversation for the current user.

    This endpoint returns the most recent conversation for the authenticated user,
    including its ID, creation date, update date, and message count.

    Returns:
        A conversation object or null if no conversations exist
    """
    # Use the authenticated user's ID
    user_id = current_user["id"]

    try:
        # Import the ConversationMemory class
        from app.memory.conversation_memory import ConversationMemory

        # Get the most recent conversation for this user
        conversation = ConversationMemory.get_most_recent_conversation(user_id)

        return {"conversation": conversation}
    except Exception as e:
        logger.error(f"Error getting most recent conversation for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting most recent conversation: {str(e)}")

@app.get("/conversations/{conversation_id}", tags=["Interaction"], summary="Get conversation messages")
async def get_conversation_messages(conversation_id: str, current_user: dict = Depends(get_current_user)):
    """
    Get all messages for a specific conversation.

    This endpoint returns a list of all messages for the specified conversation.
    Each message includes its content, role (user or assistant), and timestamp.

    Admin users can access any conversation, while regular users can only access their own conversations.

    Args:
        conversation_id: The ID of the conversation to retrieve

    Returns:
        A list of message objects
    """
    # Use the authenticated user's ID
    user_id = current_user["id"]
    is_admin = current_user.get("role") == "admin"

    try:
        # Import the ConversationMemory class
        from app.memory.conversation_memory import ConversationMemory
        from app.database.models import Conversation as DBConversation, Message as DBMessage
        from app.database.db import get_db

        # First, verify that the conversation exists and check permissions
        with get_db() as db:
            if is_admin:
                # Admin can access any conversation
                conversation = db.query(DBConversation).filter(
                    DBConversation.id == conversation_id
                ).first()
            else:
                # Regular users can only access their own conversations
                conversation = db.query(DBConversation).filter(
                    DBConversation.id == conversation_id,
                    DBConversation.user_id == user_id
                ).first()

            if not conversation:
                raise HTTPException(status_code=404, detail="Conversation not found")

            # Get messages for this conversation
            if is_admin:
                # Admin can see all messages in the conversation
                messages = db.query(DBMessage).filter(
                    DBMessage.conversation_id == conversation_id
                ).order_by(DBMessage.created_at).all()
            else:
                # Regular users can only see their own messages
                messages = db.query(DBMessage).filter(
                    DBMessage.conversation_id == conversation_id,
                    (DBMessage.user_id == user_id) | (DBMessage.user_id == None)
                ).order_by(DBMessage.created_at).all()

            # Convert to dictionaries
            message_dicts = [message.to_dict() for message in messages]

            return {"messages": message_dicts}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting messages for conversation {conversation_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting conversation messages: {str(e)}")

@app.post("/clear", response_model=IngestResponse, tags=["Interaction"], summary="Clear conversation history")
async def clear_conversation(current_user: dict = Depends(get_current_user)):
    """
    Clear the conversation history.

    This endpoint clears the conversation history maintained by the chatbot.
    It's useful when you want to start a new conversation without the context of previous interactions.

    ## Note
    You must initialize the chatbot with `/init` before using this endpoint.
    """
    # Use the authenticated user's ID
    user_id = current_user["id"]

    try:
        # Get the user's chatbot instance
        chatbot = get_chatbot(user_id)

        # Clear conversation history
        chatbot.clear_conversation()

        return IngestResponse(
            status="success",
            message=f"Conversation history cleared for user {user_id}"
        )
    except Exception as e:
        logger.error(f"Error clearing conversation for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error clearing conversation: {str(e)}")

@app.delete("/conversations/{conversation_id}", tags=["Interaction"], summary="Delete a conversation")
async def delete_conversation(conversation_id: str, current_user: dict = Depends(get_current_user)):
    """
    Delete a specific conversation and all its messages.

    This endpoint deletes a conversation and all associated messages.
    Admin users can delete any conversation, while regular users can only delete their own conversations.

    Args:
        conversation_id: The ID of the conversation to delete

    Returns:
        A success message
    """
    # Use the authenticated user's ID
    user_id = current_user["id"]
    is_admin = current_user.get("role") == "admin"

    try:
        from app.database.models import Conversation as DBConversation, Message as DBMessage
        from app.database.db import get_db

        with get_db() as db:
            # First, verify that the conversation exists and check permissions
            if is_admin:
                # Admin can delete any conversation
                conversation = db.query(DBConversation).filter(
                    DBConversation.id == conversation_id
                ).first()
            else:
                # Regular users can only delete their own conversations
                conversation = db.query(DBConversation).filter(
                    DBConversation.id == conversation_id,
                    DBConversation.user_id == user_id
                ).first()

            if not conversation:
                raise HTTPException(status_code=404, detail="Conversation not found or you don't have permission to delete it")

            # Delete all messages in the conversation
            db.query(DBMessage).filter(DBMessage.conversation_id == conversation_id).delete()

            # Delete the conversation
            db.delete(conversation)
            db.commit()

            # If this was the active conversation for the user's chatbot, clear it
            chatbot = get_chatbot(user_id)
            if chatbot and hasattr(chatbot, 'memory') and chatbot.memory.conversation_id == conversation_id:
                chatbot.memory.conversation_id = None

            return {"status": "success", "message": f"Conversation {conversation_id} deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting conversation {conversation_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting conversation: {str(e)}")

@app.post("/save", response_model=IngestResponse, tags=["Setup"], summary="Save the vector store")
async def save_vector_store(
    save_path: Optional[str] = Form(None, description="Path to save the vector store (optional)"),
    current_user: dict = Depends(get_current_user)
):
    """
    Save the vector store to disk.

    This endpoint saves the vector store to disk, allowing you to reload it later.
    If no save path is provided, the vector store will be saved to the default location specified during initialization.

    ## Note
    - You must initialize the chatbot with `/init` before using this endpoint
    - You should save the vector store after ingesting documents to avoid having to reprocess them
    - The saved vector store can be loaded with the `/load` endpoint
    """
    # Use the authenticated user's ID
    user_id = current_user["id"]

    try:
        # Get the user's chatbot instance
        chatbot = get_chatbot(user_id)

        # If no save path is provided, use the centralized vector store directory
        if not save_path:
            save_path = get_vector_store_dir()

        # Save the vector store
        chatbot.save_vector_store(save_path)

        return IngestResponse(
            status="success",
            message=f"Vector store for user {user_id} saved to {save_path}"
        )
    except Exception as e:
        logger.error(f"Error saving vector store for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error saving vector store: {str(e)}")

@app.post("/load", response_model=ConfigResponse, tags=["Setup"], summary="Load a pre-existing vector store")
async def load_chatbot(config: ConfigRequest, current_user: dict = Depends(get_current_user)):
    """
    Load a chatbot with a pre-existing vector store.

    This endpoint initializes the chatbot and loads a pre-existing vector store.
    This is useful when you have already processed documents and saved the vector store.

    ## Example with Google API Key
    ```json
    {
      "use_openai_embeddings": false,
      "use_google_embeddings": true,
      "use_google_llm": true,
      "google_api_key": "your_google_api_key",
      "vector_store_dir": "my_vector_store"
    }
    ```

    ## Example with Google Service Account
    ```json
    {
      "use_openai_embeddings": false,
      "use_google_embeddings": true,
      "use_google_llm": true,
      "google_service_account_file": ,
      "vector_store_dir": "my_vector_store"
    }
    ```

    ## Note
    - The vector store must exist at the specified location
    - You can use this instead of calling `/init` followed by document processing
    """
    # Use the authenticated user's ID
    user_id = current_user["id"]

    try:
        # If no vector store directory is provided, use the centralized vector store
        vector_store_path = config.vector_store_dir
        if not vector_store_path:
            vector_store_path = get_vector_store_dir()

        # Load the chatbot
        chatbot = app.load(
            vector_store_path=vector_store_path,
            use_openai_embeddings=config.use_openai_embeddings,
            use_google_embeddings=config.use_google_embeddings,
            use_google_llm=config.use_google_llm,
            openai_api_key=config.openai_api_key,
            google_api_key=config.google_api_key,
            google_service_account_file=config.google_service_account_file,
            google_project_id=config.google_project_id,
            embedding_model=config.embedding_model,
            llm_model_name=config.llm_model_name,
            vector_store_type=config.vector_store_type,
            use_compression=config.use_compression,
            top_k=config.top_k,
            max_memory_messages=config.max_memory_messages
        )

        # Store the chatbot in the storage
        config_dict = config.model_dump()
        config_dict["vector_store_dir"] = vector_store_path
        chatbot_storage.set(user_id, chatbot, config_dict)

        logger.info(f"Chatbot loaded from {vector_store_path} for user {user_id}")

        # Update config with actual vector store directory
        config_dict = config.model_dump()
        config_dict["vector_store_dir"] = vector_store_path

        return ConfigResponse(
            status="success",
            message=f"Chatbot loaded from {vector_store_path} for user {user_id}",
            config=config_dict
        )
    except Exception as e:
        logger.error(f"Error loading chatbot for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error loading chatbot: {str(e)}")

@app.post("/process-data-folder", response_model=IngestResponse, tags=["Document Processing"], summary="Process all PDFs in the data folder")
async def process_data_folder(
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_admin)  # Only admins can process documents
):
    """
    Process all PDF files in the data folder.

    This endpoint processes all PDF files in the 'data' folder and adds them to the vector store.
    The processing is done in the background, so the endpoint returns immediately.

    If the 'data' folder doesn't exist, it will be created.

    ## Note
    - You must initialize the chatbot with `/init` before using this endpoint
    - This is a convenient way to process all PDFs in a standard location
    - Use `/list-data-files` to see what files are available in the data folder
    """
    # Use the authenticated admin's ID (not used in this function but kept for clarity)
    # admin_id = current_user["id"]

    try:
        # Create common data directory
        common_data_dir = Path("data")
        common_data_dir.mkdir(exist_ok=True)

        # Check if there are any PDF files in the common data directory
        pdf_files = list(common_data_dir.glob("**/*.pdf"))

        if not pdf_files:
            return IngestResponse(
                status="warning",
                message="No PDF files found in the data directory. Please add PDF files to the data directory."
            )

        # Process documents for all users
        processed_count = 0
        for user_id in user_manager.users:
            # Skip if user doesn't have a chatbot instance
            user_chatbot = chatbot_storage.get(user_id)
            if user_chatbot is None:
                continue

            # Create user-specific data directory
            user_data_dir = USER_DATA_DIR / user_id / "data"
            user_data_dir.mkdir(exist_ok=True, parents=True)

            # Get all documents from the database
            with get_db() as db:
                all_documents = db.query(DBDocument).all()
                # Create a mapping of filename to document ID
                filename_to_id = {doc.filename: doc.id for doc in all_documents}

            # Copy PDFs from common directory to user directory if they don't exist there
            # and the user has access to them
            for file in pdf_files:
                # Get document ID from filename
                doc_id = filename_to_id.get(file.name)
                if not doc_id:
                    # If document is not in the database, add it and upload to S3
                    try:
                        # Upload to S3 first
                        with open(file, "rb") as f:
                            s3_key = s3_storage.upload_file(
                                f,
                                file.name,
                                "application/pdf"
                            )
                            logger.info(f"Uploaded file {file.name} to S3 with key {s3_key}")

                        # Then add to database
                        with get_db() as db:
                            document = DBDocument(
                                filename=file.name,
                                s3_key=s3_key,  # Use the actual S3 key
                                content_type="application/pdf",
                                size=os.path.getsize(file),
                                uploaded_by=user_id
                            )
                            db.add(document)
                            db.commit()
                            doc_id = document.id
                            logger.info(f"Added document {file.name} to database with ID {doc_id}")
                    except Exception as e:
                        logger.error(f"Error uploading file to S3: {str(e)}")
                        # Fallback to local path if S3 upload fails
                        with get_db() as db:
                            document = DBDocument(
                                filename=file.name,
                                s3_key=f"local/{file.name}",  # Local file path as S3 key
                                content_type="application/pdf",
                                size=os.path.getsize(file),
                                uploaded_by=user_id
                            )
                            db.add(document)
                            db.commit()
                            doc_id = document.id
                            logger.info(f"Added document {file.name} to database with ID {doc_id} (S3 upload failed)")

                # Check if user has access to this document using the document ID
                if not user_manager.user_has_document_access(user_id, doc_id):
                    continue

                target_file = user_data_dir / file.name
                if not target_file.exists():
                    import shutil
                    shutil.copy2(file, target_file)

            # Get user-specific PDF files
            user_pdf_files = list(user_data_dir.glob("**/*.pdf"))

            # Check if we've already processed these documents
            new_files = []
            for file in user_pdf_files:
                file_hash = calculate_document_hash(str(file))
                if file_hash and file_hash not in processed_documents.get(user_id, {}):
                    new_files.append(file)
                    # Initialize user's processed documents dict if it doesn't exist
                    if user_id not in processed_documents:
                        processed_documents[user_id] = {}
                    # Mark as processed
                    processed_documents[user_id][file_hash] = datetime.now()

            if new_files:
                # Start ingestion in background for this user
                background_tasks.add_task(user_chatbot.ingest_documents, str(user_data_dir), True)

                # Save the vector store after processing
                background_tasks.add_task(user_chatbot.save_vector_store)

                processed_count += len(new_files)

        if processed_count > 0:
            return IngestResponse(
                status="success",
                message=f"Started processing {processed_count} new PDF files across all users"
            )
        else:
            return IngestResponse(
                status="info",
                message="No new documents to process for any user"
            )
    except Exception as e:
        logger.error(f"Error processing data folder: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing data folder: {str(e)}")

# Image endpoint removed

# Debug image endpoint removed

@app.get("/config/google-project-id", tags=["System"], summary="Get Google project ID")
async def get_google_project_id(current_user: dict = Depends(get_current_user)):
    """
    Get the Google project ID for use in the frontend.

    This endpoint returns the Google project ID configured in the backend,
    so the frontend doesn't need to access the service account file directly.

    Returns:
        The Google project ID
    """
    return {"project_id": GOOGLE_PROJECT_ID}

@app.get("/config/google-credentials-status", tags=["System"], summary="Check Google credentials status")
async def check_google_credentials_status(current_user: dict = Depends(get_current_admin)):
    """
    Check the status of Google credentials.

    This endpoint checks if the Google credentials file exists and is properly configured.
    It also checks alternative paths where the file might be located.

    Returns:
        Detailed information about the Google credentials configuration
    """
    result = {
        "google_service_account_file": GOOGLE_SERVICE_ACCOUNT_FILE,
        "google_project_id": GOOGLE_PROJECT_ID,
        "file_exists": False,
        "env_var_set": False,
        "alternative_paths_checked": [],
        "alternative_paths_found": []
    }

    # Check if the file exists at the specified path
    if GOOGLE_SERVICE_ACCOUNT_FILE:
        result["file_exists"] = os.path.exists(GOOGLE_SERVICE_ACCOUNT_FILE)

    # Check if the environment variable is set
    result["env_var_set"] = "GOOGLE_APPLICATION_CREDENTIALS" in os.environ
    if result["env_var_set"]:
        result["env_var_value"] = os.environ["GOOGLE_APPLICATION_CREDENTIALS"]
        result["env_var_file_exists"] = os.path.exists(os.environ["GOOGLE_APPLICATION_CREDENTIALS"])

    # Check alternative paths
    alternative_paths = [
        "/app/google_credentials.json",
        os.path.join(os.path.dirname(os.path.abspath(__file__)), "google_credentials.json"),
        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "google_credentials.json"),
        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "google_credentials.json")
    ]

    for path in alternative_paths:
        result["alternative_paths_checked"].append(path)
        if os.path.exists(path):
            result["alternative_paths_found"].append(path)

    return result

@app.get("/document-status", response_model=Dict[str, Any], tags=["Document Processing"], summary="Check document processing status")
async def document_status(current_user: dict = Depends(get_current_user)):
    """
    Check the processing status of documents.

    This endpoint returns information about all documents in the database, including:
    - Document ID
    - Filename
    - Processed status
    - Number of embeddings
    - Processing timestamp
    - Groups with access to the document
    - Uploader information (username)
    """
    # Use the authenticated user's ID
    user_id = current_user["id"]
    is_admin = user_manager.is_admin(user_id)

    try:
        from sqlalchemy.exc import OperationalError, ProgrammingError

        try:
            with get_db() as db:
                try:
                    # Get all documents with uploader information
                    from app.database.models import User as DBUser

                    if is_admin:
                        # Admins can see all documents
                        documents_query = db.query(DBDocument, DBUser.username.label("uploader_name")).join(
                            DBUser, DBDocument.uploaded_by == DBUser.id
                        )
                        documents_with_uploaders = documents_query.all()
                        documents = [doc for doc, _ in documents_with_uploaders]
                        uploader_names = {doc.id: uploader_name for doc, uploader_name in documents_with_uploaders}
                    else:
                        # Regular users can only see documents they have access to
                        accessible_docs = user_manager.get_accessible_documents(user_id)
                        documents_query = db.query(DBDocument, DBUser.username.label("uploader_name")).join(
                            DBUser, DBDocument.uploaded_by == DBUser.id
                        ).filter(DBDocument.id.in_(accessible_docs))
                        documents_with_uploaders = documents_query.all()
                        documents = [doc for doc, _ in documents_with_uploaders]
                        uploader_names = {doc.id: uploader_name for doc, uploader_name in documents_with_uploaders}

                    # Get embedding counts for each document
                    doc_embedding_counts = {}
                    for doc in documents:
                        count = db.query(DBEmbedding).filter(DBEmbedding.document_id == doc.id).count()
                        doc_embedding_counts[doc.id] = count

                    # Get all groups for reference
                    all_groups = db.query(DBGroup).all()
                    group_map = {group.id: group.name for group in all_groups}

                    # Format response
                    docs_info = []
                    for doc in documents:
                        # Get groups with access to this document
                        doc_access_records = db.query(DocumentAccess).filter(DocumentAccess.document_id == doc.id).all()
                        group_ids = [record.group_id for record in doc_access_records]

                        # Map group IDs to group names
                        group_names = [group_map.get(group_id, group_id) for group_id in group_ids]

                        docs_info.append({
                            "id": doc.id,
                            "document_number": doc.document_number,  # Add document number to the response
                            "filename": doc.filename,
                            "processed": doc.processed,
                            "processed_at": doc.processed_at.isoformat() if doc.processed_at else None,
                            "embedding_count": doc_embedding_counts.get(doc.id, 0),
                            "size": doc.size,
                            "uploaded_by": doc.uploaded_by,
                            "uploader_name": uploader_names.get(doc.id, "Unknown"),  # Add uploader name
                            "uploaded_at": doc.uploaded_at.isoformat() if doc.uploaded_at else None,
                            "groups": group_names,  # Add group names to the response
                            # Include the new metadata fields
                            "service_name": doc.service_name,
                            "software_menus": doc.software_menus,
                            "issue_type": doc.issue_type
                        })

                    return {
                        "status": "success",
                        "message": f"Found {len(docs_info)} documents",
                        "documents": docs_info
                    }
                except (OperationalError, ProgrammingError) as db_error:
                    # Handle case where tables don't exist
                    logger.error(f"Database error in document status: {str(db_error)}")
                    return {
                        "status": "error",
                        "message": "Database tables not properly initialized",
                        "documents": []
                    }
        except Exception as db_conn_error:
            # Handle database connection errors
            logger.error(f"Database connection error: {str(db_conn_error)}")
            return {
                "status": "error",
                "message": "Database connection error",
                "documents": []
            }
    except Exception as e:
        logger.error(f"Error getting document status: {str(e)}")
        # Return empty data instead of raising an exception
        return {
            "status": "error",
            "message": f"Error getting document status: {str(e)}",
            "documents": []
        }

@app.delete("/documents/{document_id}", tags=["Document Processing"], summary="Delete a document (admin only)")
async def delete_document(document_id: str, current_user: dict = Depends(get_current_admin)):
    """
    Delete a document from the system.

    This endpoint will:
    1. Delete the document from the database
    2. Delete all embeddings associated with the document
    3. Delete the document from S3
    4. Remove the document from all vector stores

    WARNING: This is a destructive operation and cannot be undone.
    Only administrators can perform this operation.
    """
    logger.info(f"Delete document request received for document ID: {document_id}")

    # Use the standard document deletion process
    return await standard_delete_document(document_id)

@app.delete("/documents/{document_id}/complete", tags=["Document Processing"], summary="Completely delete a document (admin only)")
async def complete_delete_document(document_id: str, current_user: dict = Depends(get_current_admin)):
    """
    Completely delete a document from all storage locations.

    This endpoint will:
    1. Delete the document from the database
    2. Delete all embeddings associated with the document
    3. Delete the document from S3
    4. Delete the document from all vector stores using aggressive methods
    5. Delete any local copies of the document
    6. Force reinitialization of all chatbots

    This is a more thorough deletion process than the standard delete endpoint.
    Use this when documents are still accessible after using the standard delete.

    WARNING: This is a destructive operation and cannot be undone.
    Only administrators can perform this operation.
    """
    from app.api.complete_document_deletion import completely_delete_document

    logger.info(f"Complete document deletion request received for document ID: {document_id}")

    # Perform complete document deletion
    results = completely_delete_document(
        document_id=document_id,
        s3_storage=s3_storage,
        chatbot_storage=chatbot_storage,
        user_data_dir=USER_DATA_DIR,
        data_dir=Path("data")
    )

    if results["success"]:
        return {
            "status": "success",
            "message": f"Document '{results.get('filename', '')}' (ID: {document_id}) completely deleted",
            "details": results
        }
    else:
        error_msg = f"Error completely deleting document {document_id}: {', '.join(results['errors'])}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail=error_msg)

async def standard_delete_document(document_id: str):
    """
    Standard document deletion process.

    This function:
    1. Deletes the document from the database
    2. Deletes all embeddings associated with the document
    3. Deletes all document access records
    4. Deletes the document from S3
    5. Removes the document from all vector stores
    6. Forces reinitialization of all chatbots

    Args:
        document_id: The ID of the document to delete

    Returns:
        Dictionary with deletion results
    """
    try:
        # Get document details from database
        with get_db() as db:
            document = db.query(DBDocument).filter(DBDocument.id == document_id).first()

            if not document:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Document with ID {document_id} not found"
                )

            # Get the S3 key
            s3_key = document.s3_key
            filename = document.filename

            # Delete embeddings first (they have foreign key constraints)
            embedding_count = db.query(DBEmbedding).filter(DBEmbedding.document_id == document_id).count()
            db.query(DBEmbedding).filter(DBEmbedding.document_id == document_id).delete()
            logger.info(f"Deleted {embedding_count} embeddings for document {document_id}")

            # Delete document access records
            access_count = db.query(DocumentAccess).filter(DocumentAccess.document_id == document_id).count()
            db.query(DocumentAccess).filter(DocumentAccess.document_id == document_id).delete()
            logger.info(f"Deleted {access_count} access records for document {document_id}")

            # Delete the document from the database
            db.delete(document)
            db.commit()
            logger.info(f"Deleted document {document_id} from database")

        # Delete the file from S3
        s3_deleted = False
        if s3_key and not s3_key.startswith('local/'):
            try:
                s3_storage.delete_file(s3_key)
                logger.info(f"Deleted file {s3_key} from S3")
                s3_deleted = True
            except Exception as e:
                logger.error(f"Error deleting file from S3: {str(e)}")
                # Continue with the operation even if S3 deletion fails

        # Clear the document from all chatbots' vector stores and reinitialize them
        # This is a best-effort operation - we can't guarantee all vector stores will be updated
        deletion_results = {}
        try:
            # Get all chatbots from storage
            all_chatbots = chatbot_storage.get_all_chatbots()
            logger.info(f"Found {len(all_chatbots)} chatbots in memory to update")

            for user_id, chatbot in all_chatbots.items():
                try:
                    # Use the delete_document method which handles everything
                    if chatbot:
                        success = chatbot.delete_document(document_id)
                        deletion_results[user_id] = success
                        if success:
                            logger.info(f"Successfully deleted document {document_id} from user {user_id}'s chatbot")

                            # Update the chatbot in storage to ensure the changes persist
                            chatbot_storage.update(user_id, chatbot)
                            logger.info(f"Updated chatbot for user {user_id} in storage after document deletion")
                        else:
                            logger.warning(f"Failed to delete document {document_id} from user {user_id}'s chatbot")
                    else:
                        logger.warning(f"Chatbot for user {user_id} is None, skipping document deletion")
                        deletion_results[user_id] = False
                except Exception as e:
                    logger.error(f"Error deleting document from chatbot for user {user_id}: {str(e)}")
                    deletion_results[user_id] = False

            # Log overall results
            success_count = sum(1 for success in deletion_results.values() if success)
            logger.info(f"Successfully deleted document {document_id} from {success_count} out of {len(all_chatbots)} chatbots")
        except Exception as e:
            logger.error(f"Error getting chatbots from storage: {str(e)}")
            # Continue with the operation even if vector store cleanup fails

        # Force reinitialization of all chatbots on next use
        # This ensures that even if the deletion wasn't completely successful,
        # the chatbots will be reinitialized with the updated document set
        chatbots_cleared = False
        try:
            # Clear all chatbots from memory
            chatbot_storage.clear_all()
            logger.info("Cleared all chatbot instances from memory to force reinitialization")
            chatbots_cleared = True
        except Exception as e:
            logger.error(f"Error clearing chatbot instances: {str(e)}")

        # Create notifications for all users
        try:
            # Import the simple notification service
            from app.services.simple_notification import send_notification_to_all_users

            logger.info(f"Creating document deletion notification for document {filename}")

            # Send a notification to all users
            notification_result = send_notification_to_all_users(
                title="Document Removed",
                message=f"The document '{filename}' has been removed from the system.",
                notification_type="document_deleted",
                created_by=None,  # System notification
                document_id=None  # Document is deleted, so no ID
            )

            if notification_result:
                logger.info(f"Successfully sent document deletion notification to all users for document {filename}")
            else:
                logger.error(f"Failed to send document deletion notification for document {filename}")

            # Double-check that notifications were created
            with get_db() as db:
                from app.database.models import Notification
                notification_count = db.query(Notification).filter(
                    Notification.type == "document_deleted",
                    Notification.created_at > (datetime.now() - timedelta(minutes=1))
                ).count()

                logger.info(f"Found {notification_count} recent document deletion notifications in the database")
        except Exception as e:
            logger.error(f"Error creating notification for document deletion: {str(e)}", exc_info=True)

            # Record activity
            ActivityService.record_activity(
                user_id=None,  # System activity
                action="document_delete",
                document_id=None,  # Document is deleted
                details={
                    "filename": filename,
                    "document_id": document_id
                }
            )
        except Exception as e:
            logger.error(f"Error creating notifications for document deletion: {str(e)}")
            # Continue with the operation even if notification creation fails

        return {
            "status": "success",
            "message": f"Document '{filename}' (ID: {document_id}) deleted successfully",
            "details": {
                "document_id": document_id,
                "filename": filename,
                "database_deletion": True,
                "embeddings_deleted": embedding_count,
                "access_records_deleted": access_count,
                "s3_deletion": s3_deleted,
                "vector_store_deletions": deletion_results,
                "chatbots_cleared": chatbots_cleared
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting document {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting document: {str(e)}")

@app.post("/reset-vector-store", response_model=Dict[str, Any], tags=["System Maintenance"], summary="Reset the vector store for a specific user")
async def reset_vector_store(user_id: str, current_user: dict = Depends(get_current_admin)):
    """
    Reset the vector store for a specific user.

    This endpoint will:
    1. Reset the vector store for the specified user
    2. Reinitialize the chatbot with an empty vector store

    This is useful when the vector store is in an inconsistent state and causing errors.
    Only administrators can perform this operation.

    ## Response Format
    ```json
    {
      "status": "success",
      "message": "Vector store reset successfully for user {user_id}",
      "details": {
        "user_id": "user-id",
        "success": true
      }
    }
    ```
    """
    try:
        # Get the chatbot for the user
        try:
            # Try to get the chatbot from storage first
            chatbot = chatbot_storage.get(user_id)

            # If not in storage, initialize a new one
            if not chatbot:
                # Use the centralized vector store
                vector_store_dir = get_vector_store_dir()
                logger.info(f"Using centralized vector store directory: {vector_store_dir} for user {user_id}")

                # Create a new chatbot
                chatbot = RAGChatbot(
                    use_google_embeddings=False,  # Use HuggingFace embeddings
                    use_google_llm=True,
                    google_service_account_file=GOOGLE_SERVICE_ACCOUNT_FILE,
                    google_project_id=GOOGLE_PROJECT_ID,
                    vector_store_type="chroma",
                    vector_store_dir=vector_store_dir,
                    user_id=user_id  # Pass user_id for access control
                )
                logger.info(f"Created new chatbot for user {user_id}")
        except Exception as e:
            logger.error(f"Error getting or creating chatbot for user {user_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error initializing chatbot: {str(e)}")

        if not chatbot:
            raise HTTPException(status_code=404, detail=f"Chatbot not found for user {user_id}")

        # Reset the vector store
        success = chatbot.reset_vector_store()

        if success:
            # Update the chatbot in storage
            chatbot_storage.update(user_id, chatbot)
            logger.info(f"Reset vector store for user {user_id}")

            return {
                "status": "success",
                "message": f"Vector store reset successfully for user {user_id}",
                "details": {
                    "user_id": user_id,
                    "success": True
                }
            }
        else:
            return {
                "status": "error",
                "message": f"Failed to reset vector store for user {user_id}",
                "details": {
                    "user_id": user_id,
                    "success": False
                }
            }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resetting vector store for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error resetting vector store: {str(e)}")

@app.post("/reset-all-vector-stores", response_model=Dict[str, Any], tags=["System Maintenance"], summary="Reset vector stores for all users")
async def reset_all_vector_stores(current_user: dict = Depends(get_current_admin)):
    """
    Reset vector stores for all users.

    This endpoint will:
    1. Get all chatbots from storage
    2. Reset the vector store for each chatbot
    3. Reinitialize all chatbots with empty vector stores

    This is useful when vector stores are in an inconsistent state and causing errors.
    Only administrators can perform this operation.

    ## Response Format
    ```json
    {
      "status": "success",
      "message": "Reset vector stores for all users",
      "details": {
        "total_users": 5,
        "successful_resets": 5,
        "failed_resets": 0,
        "results": {
          "user-id-1": true,
          "user-id-2": true,
          ...
        }
      }
    }
    ```
    """
    try:
        # Reset the centralized vector store
        try:
            # Get the centralized vector store directory
            vector_store_dir = get_vector_store_dir()
            logger.info(f"Resetting centralized vector store at {vector_store_dir}")

            # Delete the vector store directory if it exists
            if os.path.exists(vector_store_dir):
                import shutil
                shutil.rmtree(vector_store_dir)
                logger.info(f"Deleted centralized vector store directory: {vector_store_dir}")

            # Create a new empty directory
            os.makedirs(vector_store_dir, exist_ok=True)
            logger.info(f"Created new centralized vector store directory: {vector_store_dir}")

            # Create a new empty vector store
            from langchain_community.vectorstores import Chroma
            from app.embedding.embedder import Embedder

            # Initialize embeddings with HuggingFace
            embedder = Embedder(embedding_model="all-MiniLM-L6-v2")

            # Create a new Chroma vector store
            vector_store = Chroma(
                embedding_function=embedder.embeddings,
                persist_directory=vector_store_dir
            )
            vector_store.persist()
            logger.info(f"Created new empty Chroma vector store at {vector_store_dir}")

            # Clear all chatbots from storage to force reinitialization
            chatbot_storage.clear_all()
            logger.info("Cleared all chatbot instances from memory to force reinitialization")

            return {
                "status": "success",
                "message": "Reset centralized vector store successfully",
                "details": {
                    "vector_store_dir": vector_store_dir,
                    "chatbots_cleared": True
                }
            }
        except Exception as e:
            logger.error(f"Error resetting centralized vector store: {str(e)}")
            return {
                "status": "error",
                "message": f"Error resetting centralized vector store: {str(e)}",
                "details": {
                    "error": str(e)
                }
            }
    except Exception as e:
        logger.error(f"Error resetting vector stores for all users: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error resetting vector stores: {str(e)}")

@app.post("/hard-reset", response_model=Dict[str, Any], tags=["System Maintenance"], summary="Perform a hard reset of the system (admin only)")
async def perform_hard_reset(current_user: dict = Depends(get_current_admin)):
    """
    Perform a hard reset of the system.

    This endpoint will:
    1. Delete all documents from the database
    2. Delete all embeddings from the database
    3. Delete all document access records
    4. Delete all chatbot configurations
    5. Delete all files from S3
    6. Delete any local vector store directories
    7. Delete user data directories
    8. Delete data directory contents
    9. Clear all chatbot instances from memory

    WARNING: This is a destructive operation and cannot be undone.
    Only administrators can perform this operation.

    ## Response Format
    ```json
    {
      "status": "success",
      "message": "System reset successfully",
      "details": {
        "documents_deleted": 10,
        "embeddings_deleted": 1000,
        "document_access_deleted": 5,
        "chatbot_configs_deleted": 3,
        "s3_files_deleted": 10,
        "vector_stores_deleted": true,
        "user_data_deleted": true,
        "data_dir_cleaned": true,
        "errors": []
      }
    }
    ```
    """
    try:
        # First clear all chatbot instances from memory
        chatbot_storage.clear_all()
        logger.info("Cleared all chatbot instances from memory before hard reset")

        # Perform the hard reset
        results = hard_reset_system()

        # Clear all chatbot instances again to be sure
        chatbot_storage.clear_all()
        logger.info("Cleared all chatbot instances from memory after hard reset")

        # Create a notification for all users about the hard reset
        try:
            # Import the simple notification service
            from app.services.simple_notification import send_notification_to_all_users

            logger.info("Creating system reset notification for all users")

            # Send a notification to all users
            notification_result = send_notification_to_all_users(
                title="System Reset",
                message="The system has been completely reset. All documents and embeddings have been removed.",
                notification_type="system_reset",
                created_by=current_user["id"]
            )

            if notification_result:
                logger.info("Successfully sent system reset notification to all users")
            else:
                logger.error("Failed to send system reset notification")

            # Double-check that notifications were created
            with get_db() as db:
                from app.database.models import Notification
                notification_count = db.query(Notification).filter(
                    Notification.type == "system_reset",
                    Notification.created_at > (datetime.now() - timedelta(minutes=1))
                ).count()

                logger.info(f"Found {notification_count} recent system reset notifications in the database")
        except Exception as e:
            logger.error(f"Error creating notification for system reset: {str(e)}", exc_info=True)

        # Record the activity
        ActivityService.record_activity(
            user_id=current_user["id"],
            action="system_reset",
            details={"result": "success" if results["success"] else "error"}
        )

        if results["success"]:
            return {
                "status": "success",
                "message": "System reset successfully",
                "details": results
            }
        else:
            return {
                "status": "error",
                "message": "Error performing system reset",
                "details": results
            }
    except Exception as e:
        logger.error(f"Error performing hard reset: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error performing hard reset: {str(e)}")

# Notification endpoints
@app.get("/notifications", tags=["Notifications"], summary="Get notifications for the current user")
async def get_notifications(
    limit: int = 20,
    include_read: bool = False,
    current_user: dict = Depends(get_current_user)
):
    """
    Get notifications for the current user.

    This endpoint returns notifications for the authenticated user.

    Args:
        limit: Maximum number of notifications to return
        include_read: Whether to include read notifications

    Returns:
        A list of notifications
    """
    user_id = current_user["id"]
    is_admin = current_user.get("role") == "admin"

    try:
        if is_admin:
            # Admin users can see all notifications
            notifications = NotificationService.get_all_notifications(limit, include_read)
        else:
            # Regular users can only see their own notifications
            notifications = NotificationService.get_user_notifications(user_id, limit, include_read)

        return {"notifications": notifications}
    except Exception as e:
        logger.error(f"Error getting notifications for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting notifications: {str(e)}")

@app.get("/notifications/unread-count", tags=["Notifications"], summary="Get unread notification count")
async def get_unread_count(current_user: dict = Depends(get_current_user)):
    """
    Get the number of unread notifications for the current user.

    Returns:
        The number of unread notifications
    """
    user_id = current_user["id"]

    try:
        count = NotificationService.get_unread_count(user_id)
        return {"count": count}
    except Exception as e:
        logger.error(f"Error getting unread count for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting unread count: {str(e)}")

@app.get("/notifications/check-new", tags=["Notifications"], summary="Check for new notifications efficiently")
async def check_new_notifications(
    last_checked: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """
    Check if there are new notifications since the last check.

    This endpoint is optimized for frequent polling as it only returns
    a boolean indicating if there are new notifications, not the actual notifications.

    Args:
        last_checked: ISO format timestamp of when notifications were last checked

    Returns:
        Boolean indicating if there are new notifications
    """
    user_id = current_user["id"]

    try:
        # Convert ISO timestamp to datetime if provided
        last_checked_dt = None
        if last_checked:
            try:
                # Handle both formats: with and without Z
                if 'Z' in last_checked:
                    last_checked_dt = datetime.fromisoformat(last_checked.replace('Z', '+00:00'))
                else:
                    last_checked_dt = datetime.fromisoformat(last_checked)
            except ValueError as e:
                # Log the error but continue with None
                logger.warning(f"Invalid timestamp format: {last_checked}, error: {str(e)}")
                pass

        # If we couldn't parse the timestamp or it's too old, use a recent timestamp
        if not last_checked_dt:
            # Use a timestamp from 10 seconds ago to catch very recent notifications
            last_checked_dt = datetime.now(datetime.timezone.utc) - timedelta(seconds=10)
            logger.info(f"Using recent timestamp for user {user_id}: {last_checked_dt}")

        # Check for any unread notifications
        has_new = NotificationService.has_new_notifications(user_id, last_checked_dt)

        # Log for debugging
        logger.info(f"Check new notifications for user {user_id}, last_checked: {last_checked_dt}, has_new: {has_new}")

        # If no new notifications found, check for any unread notifications as a fallback
        if not has_new:
            unread_count = NotificationService.get_unread_count(user_id)
            if unread_count > 0:
                logger.info(f"No new notifications since {last_checked_dt}, but found {unread_count} total unread notifications for user {user_id}")
                has_new = True

        return {"has_new": has_new}
    except Exception as e:
        logger.error(f"Error checking for new notifications for user {user_id}: {str(e)}")
        # Return false instead of raising an exception to avoid disrupting the UI
        return {"has_new": False}

@app.post("/notifications", tags=["Notifications"], summary="Create a notification (admin only)")
async def create_notification(
    notification: NotificationCreate,
    current_user: dict = Depends(get_current_admin)
):
    """
    Create a new notification.

    This endpoint creates a new notification for a specific user or all users.

    Args:
        notification: Notification details

    Returns:
        The created notification
    """
    try:
        created = NotificationService.create_notification(
            title=notification.title,
            message=notification.message,
            notification_type=notification.type,
            created_by=current_user["id"],
            user_id=notification.user_id,
            document_id=notification.document_id,
            is_global=notification.is_global
        )

        return {"notification": created.to_dict() if hasattr(created, 'to_dict') else created}
    except Exception as e:
        logger.error(f"Error creating notification: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating notification: {str(e)}")

@app.put("/notifications/{notification_id}", tags=["Notifications"], summary="Mark a notification as read")
async def mark_notification_as_read(
    notification_id: str,
    update: NotificationUpdate,
    current_user: dict = Depends(get_current_user)
):
    """
    Mark a notification as read.

    This endpoint marks a notification as read for the current user.

    Args:
        notification_id: ID of the notification
        update: Update details

    Returns:
        Success message
    """
    user_id = current_user["id"]
    is_admin = current_user.get("role") == "admin"

    try:
        success = NotificationService.mark_as_read(notification_id, user_id, is_admin)

        if not success:
            raise HTTPException(status_code=404, detail="Notification not found or you don't have permission to update it")

        return {"message": "Notification marked as read"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error marking notification {notification_id} as read: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error marking notification as read: {str(e)}")

@app.put("/notifications/mark-all-read", tags=["Notifications"], summary="Mark all notifications as read")
async def mark_all_notifications_as_read(current_user: dict = Depends(get_current_user)):
    """
    Mark all notifications as read for the current user.

    Returns:
        Success message
    """
    user_id = current_user["id"]
    is_admin = current_user.get("role") == "admin"

    try:
        success = NotificationService.mark_all_as_read(user_id, is_admin)

        if not success:
            raise HTTPException(status_code=500, detail="Error marking all notifications as read")

        return {"message": "All notifications marked as read"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error marking all notifications as read for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error marking all notifications as read: {str(e)}")

@app.delete("/notifications/{notification_id}", tags=["Notifications"], summary="Delete a notification")
async def delete_notification(
    notification_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Delete a notification.

    This endpoint deletes a notification for the current user.
    Admin users can delete any notification.

    Args:
        notification_id: ID of the notification

    Returns:
        Success message
    """
    user_id = current_user["id"]
    is_admin = current_user.get("role") == "admin"

    try:
        success = NotificationService.delete_notification(notification_id, user_id, is_admin)

        if not success:
            raise HTTPException(status_code=404, detail="Notification not found or you don't have permission to delete it")

        return {"message": "Notification deleted"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting notification {notification_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting notification: {str(e)}")

# Analytics endpoints
@app.get("/analytics/dashboard", tags=["Analytics"], summary="Get dashboard analytics data (admin only)")
async def get_dashboard_analytics(current_user: dict = Depends(get_current_admin)):
    """
    Get analytics data for the admin dashboard.

    This endpoint returns statistics about user activity, document usage, etc.

    Returns:
        Dashboard analytics data
    """
    try:
        stats = ActivityService.get_activity_stats()
        return stats
    except Exception as e:
        logger.error(f"Error getting dashboard analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting dashboard analytics: {str(e)}")

@app.get("/analytics/notification-stats", tags=["Analytics"], summary="Get notification read statistics (admin only)")
async def get_notification_stats(current_user: dict = Depends(get_current_admin)):
    """
    Get statistics about which notifications have been read by which users.

    This endpoint returns data for visualizing notification read status across users.

    Returns:
        Notification statistics data
    """
    try:
        from app.database.models import Notification, NotificationReadStatus, User, Document
        from sqlalchemy import func
        from sqlalchemy.exc import OperationalError, ProgrammingError

        try:
            with get_db() as db:
                # Check if the notifications table exists
                try:
                    # Get all notifications
                    notifications = db.query(Notification).order_by(Notification.created_at.desc()).limit(50).all()

                    # Get all users
                    users = db.query(User).all()

                    # Get all documents
                    documents = db.query(Document).all()

                    # Prepare the result
                    result = {
                        "notifications": [],
                        "users": [{"id": user.id, "username": user.username} for user in users],
                        "documents": [{"id": doc.id, "filename": doc.filename} for doc in documents],
                        "read_status": []
                    }

                    # Add notification data
                    for notification in notifications:
                        result["notifications"].append({
                            "id": notification.id,
                            "title": notification.title,
                            "created_at": notification.created_at.isoformat() if notification.created_at else None,
                            "type": notification.type,
                            "document_id": notification.document_id
                        })

                        # Get read status for this notification
                        read_statuses = db.query(NotificationReadStatus).filter(
                            NotificationReadStatus.notification_id == notification.id
                        ).all()

                        # Create a map of user_id -> read_status
                        read_map = {status.user_id: status.read_at.isoformat() for status in read_statuses}

                        # Add read status for each user
                        for user in users:
                            result["read_status"].append({
                                "notification_id": notification.id,
                                "user_id": user.id,
                                "read_at": read_map.get(user.id, None)
                            })

                    # Add summary statistics
                    result["summary"] = {
                        "total_notifications": len(notifications),
                        "total_users": len(users),
                        "read_count": db.query(NotificationReadStatus).count(),
                        "unread_count": len(notifications) * len(users) - db.query(NotificationReadStatus).count()
                    }

                    return result
                except (OperationalError, ProgrammingError) as db_error:
                    # Handle case where tables don't exist
                    logger.error(f"Database error in notification stats: {str(db_error)}")
                    return {
                        "notifications": [],
                        "users": [],
                        "documents": [],
                        "read_status": [],
                        "summary": {
                            "total_notifications": 0,
                            "total_users": 0,
                            "read_count": 0,
                            "unread_count": 0,
                            "error": "Database tables not properly initialized"
                        }
                    }
        except Exception as db_conn_error:
            # Handle database connection errors
            logger.error(f"Database connection error: {str(db_conn_error)}")
            return {
                "notifications": [],
                "users": [],
                "documents": [],
                "read_status": [],
                "summary": {
                    "total_notifications": 0,
                    "total_users": 0,
                    "read_count": 0,
                    "unread_count": 0,
                    "error": "Database connection error"
                }
            }
    except Exception as e:
        logger.error(f"Error getting notification statistics: {str(e)}")
        # Return empty data instead of raising an exception
        return {
            "notifications": [],
            "users": [],
            "documents": [],
            "read_status": [],
            "summary": {
                "total_notifications": 0,
                "total_users": 0,
                "read_count": 0,
                "unread_count": 0,
                "error": str(e)
            }
        }

@app.get("/analytics/user-activities/{user_id}", tags=["Analytics"], summary="Get activities for a specific user (admin only)")
async def get_user_activities(
    user_id: str,
    limit: int = 50,
    current_user: dict = Depends(get_current_admin)
):
    """
    Get activities for a specific user.

    This endpoint returns a list of activities for a specific user.

    Args:
        user_id: ID of the user
        limit: Maximum number of activities to return

    Returns:
        A list of activities
    """
    try:
        activities = ActivityService.get_user_activities(user_id, limit)
        return {"activities": activities}
    except Exception as e:
        logger.error(f"Error getting activities for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting user activities: {str(e)}")

@app.get("/analytics/document-activities/{document_id}", tags=["Analytics"], summary="Get activities for a specific document (admin only)")
async def get_document_activities(
    document_id: str,
    limit: int = 50,
    current_user: dict = Depends(get_current_admin)
):
    """
    Get activities for a specific document.

    This endpoint returns a list of activities for a specific document.

    Args:
        document_id: ID of the document
        limit: Maximum number of activities to return

    Returns:
        A list of activities
    """
    try:
        activities = ActivityService.get_document_activities(document_id, limit)
        return {"activities": activities}
    except Exception as e:
        logger.error(f"Error getting activities for document {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting document activities: {str(e)}")

@app.get("/health", tags=["System"], summary="Health check endpoint")
async def health_check():
    """
    Health check endpoint that verifies database connectivity and system status.

    This endpoint can be used by load balancers and monitoring systems to check
    if the application is healthy and ready to serve requests.

    Returns:
        Dictionary with health status and database connectivity information
    """
    try:
        from app.database.db import test_database_connection

        # Test database connection
        db_healthy = test_database_connection(max_retries=1)

        health_status = {
            "status": "healthy" if db_healthy else "unhealthy",
            "database": "connected" if db_healthy else "disconnected",
            "timestamp": datetime.utcnow().isoformat(),
            "environment": os.getenv("ENV", "unknown")
        }

        if db_healthy:
            return health_status
        else:
            raise HTTPException(status_code=503, detail=health_status)

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        error_status = {
            "status": "unhealthy",
            "database": "error",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat(),
            "environment": os.getenv("ENV", "unknown")
        }
        raise HTTPException(status_code=503, detail=error_status)

@app.get("/list-data-files", response_model=Dict[str, Any], tags=["Document Processing"], summary="List all PDF files in the data folder")
async def list_data_files(current_user: dict = Depends(get_current_user)):
    """
    List all PDF files in the data folder.

    This endpoint returns information about all PDF files in the 'data' folder, including:
    - File name
    - Relative path
    - File size
    - Last modified timestamp

    If the 'data' folder doesn't exist, it will be created.

    ## Response Format
    ```json
    {
      "status": "success",
      "message": "Found X PDF files",
      "files": [
        {
          "name": "document.pdf",
          "path": "document.pdf",
          "size": 12345,
          "modified": 1617293932.123
        }
      ]
    }
    ```
    """
    # Use the authenticated user's ID
    user_id = current_user["id"]
    is_admin = user_manager.is_admin(user_id)

    try:
        files_info = []

        # If admin, show all files in the common data directory
        if is_admin:
            common_data_dir = Path("data")
            if common_data_dir.exists():
                common_pdf_files = list(common_data_dir.glob("**/*.pdf"))

                # Get all documents from the database
                with get_db() as db:
                    all_documents = db.query(DBDocument).all()
                    # Create a mapping of filename to document ID and processed status
                    filename_to_id = {doc.filename: doc.id for doc in all_documents}
                    filename_to_processed = {doc.filename: doc.processed for doc in all_documents}

                for file in common_pdf_files:
                    # Get document ID from filename
                    doc_id = filename_to_id.get(file.name)
                    if doc_id:
                        # Get groups with access to this document using the document ID
                        access_groups = user_manager.get_document_access(doc_id)
                        # Get processed status
                        processed = filename_to_processed.get(file.name, False)
                    else:
                        # If document is not in the database, it has no access control
                        access_groups = []
                        processed = False

                    # Get embedding count if document is in database
                    embedding_count = 0
                    if doc_id:
                        with get_db() as db:
                            embedding_count = db.query(DBEmbedding).filter(DBEmbedding.document_id == doc_id).count()

                    files_info.append({
                        "name": file.name,
                        "path": str(file.relative_to(common_data_dir)),
                        "size": file.stat().st_size,
                        "modified": file.stat().st_mtime,
                        "location": "common",
                        "access_groups": access_groups,
                        "doc_id": doc_id,
                        "processed": processed,
                        "embedding_count": embedding_count
                    })

        # For regular users, show documents they have access to
        # Get all documents from the database
        with get_db() as db:
            all_documents = db.query(DBDocument).all()
            # Create a mapping of filename to document ID
            filename_to_id = {doc.filename: doc.id for doc in all_documents}
            id_to_filename = {doc.id: doc.filename for doc in all_documents}

        # Get all documents the user has access to
        accessible_docs = user_manager.get_accessible_documents(user_id)
        logger.info(f"User {user_id} has access to {len(accessible_docs)} documents: {accessible_docs}")

        # Check user-specific data directory
        user_data_dir = USER_DATA_DIR / user_id / "data"
        user_data_dir.mkdir(exist_ok=True, parents=True)

        # For each accessible document, check if it exists in the user's directory
        # If not, copy it from the common directory
        common_data_dir = Path("data")
        if common_data_dir.exists():
            for doc_id in accessible_docs:
                # Get filename from document ID
                filename = id_to_filename.get(doc_id)
                if not filename:
                    continue

                # Check if file exists in user's directory
                user_file = user_data_dir / filename
                if not user_file.exists():
                    # Check if file exists in common directory
                    common_file = common_data_dir / filename
                    if common_file.exists():
                        # Copy file to user's directory
                        import shutil
                        shutil.copy2(common_file, user_file)
                        logger.info(f"Copied {filename} to user {user_id}'s directory")

        # Now list all files in the user's directory
        if user_data_dir.exists():
            user_pdf_files = list(user_data_dir.glob("**/*.pdf"))

            for file in user_pdf_files:
                # Check if file has been processed
                file_hash = calculate_document_hash(str(file))
                is_processed = file_hash in processed_documents.get(user_id, {})

                # Get document ID from filename
                doc_id = filename_to_id.get(file.name)

                # Get embedding count if document is in database
                embedding_count = 0
                if doc_id:
                    with get_db() as db:
                        embedding_count = db.query(DBEmbedding).filter(DBEmbedding.document_id == doc_id).count()

                files_info.append({
                    "name": file.name,
                    "path": str(file.relative_to(user_data_dir)),
                    "size": file.stat().st_size,
                    "modified": file.stat().st_mtime,
                    "location": "user",
                    "processed": is_processed,
                    "doc_id": doc_id,
                    "embedding_count": embedding_count
                })

        return {
            "status": "success",
            "message": f"Found {len(files_info)} PDF files for user {user_id}",
            "files": files_info,
            "is_admin": is_admin
        }
    except Exception as e:
        logger.error(f"Error listing data files for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error listing data files: {str(e)}")

# Run the app
def start():
    """
    Start the FastAPI server.
    """
    uvicorn.run("app.api.app:app", host="0.0.0.0", port=8000, reload=True)

if __name__ == "__main__":
    start()
